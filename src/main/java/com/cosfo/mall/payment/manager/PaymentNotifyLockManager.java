package com.cosfo.mall.payment.manager;

import com.cosfo.mall.common.constants.RedisKeyEnum;
import com.cosfo.mall.common.context.PayNotifyBizType;
import com.github.javaparser.utils.Log;
import net.xianmu.common.exception.ProviderException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: George
 * @date: 2024-08-19
 **/
@Component
public class PaymentNotifyLockManager {

    @Resource
    private RedissonClient redissonClient;

    public RLock lock(String paymentNo, String bizType) {
        // 加锁
        String redisKey = RedisKeyEnum.C00017.join(paymentNo, bizType);
        RLock lock = redissonClient.getLock(redisKey);
        // 未获取到锁，退出
        try {
            if (!lock.tryLock(1, TimeUnit.MINUTES)) {
                throw new ProviderException("该支付单回调正在处理中，请勿重复操作");
            }
        } catch (Exception e) {
            throw new ProviderException("支付单回调处理失败");
        }
        return lock;
    }
}
