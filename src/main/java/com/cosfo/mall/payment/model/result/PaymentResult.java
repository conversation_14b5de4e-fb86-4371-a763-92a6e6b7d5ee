package com.cosfo.mall.payment.model.result;

import com.cosfo.mall.common.constants.PayTypeFlagEnum;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.payment.model.dto.OrderPayResultDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 支付结果对象
 * @author: George
 * @date: 2023-08-29
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentResult {

    /**
     * true 成功 false 失败
     */
    private boolean isSuccess;

    /**
     * 响应码
     */
    private ResultDTOEnum code;

    /**
     * 预支付id
     */
    private String prepayId;

    /**
     * 时间戳
     */
    private String timeStamp;
    /**
     * 随机字符串
     */
    private String nonceStr;
    /**
     * 小程序下单接口返回的prepay_id参数值，提交格式如：prepay_id=***
     */
    @JsonProperty("package")
    private String packageStr;
    /**
     * 签名方式
     */
    private String signType;
    /**
     * 签名
     */
    private String paySign;

    /**
     * 公众号appId
     */
    private String oaAppId;

    /**
     * qrCode
     */
    private String qrCode;

    /**
     * 订单号集合
     */
    private List<String> orderNos;

    /**
     * 订单支付结果dto
     */
    private OrderPayResultDTO orderPayResultDTO;

    /**
     * 支付单id
     */
    private Long paymentId;

    /**
     * 交易金额
     */
    private BigDecimal transAmt;

    /**
     * 支付类型标记 0:未明确；1:小程序汇付插件支付
     */
    private Integer payTypeFlag = PayTypeFlagEnum.UNKNOWN.getCode();
    /**
     * 线下 支付凭证
     */
    private String paymentReceipt;
}
