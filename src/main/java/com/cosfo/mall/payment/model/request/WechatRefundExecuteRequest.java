package com.cosfo.mall.payment.model.request;

import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.wechat.bean.base.Amount;
import lombok.Data;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-09-08
 **/
@Data
public class WechatRefundExecuteRequest extends RefundExecuteRequest {

    private String out_trade_no;

    private String out_refund_no;

    private String reason;

    private String notify_url;

    private String funds_account;

    private Amount amount;

    private transient Refund refund;

    /**
     * 商户/服务商id
     */
    private transient String spMchid;
}
