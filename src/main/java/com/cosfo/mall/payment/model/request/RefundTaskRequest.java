package com.cosfo.mall.payment.model.request;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 执行退款任务请求对象
 * @author: <PERSON>
 * @date: 2023-09-08
 **/
@Data
public class RefundTaskRequest {

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态集合
     */
    private List<Integer> statusList;

    /**
     * 最大重试次数
     */
    private Integer maxRetryNum;

    /**
     * 限制数
     */
    private Integer limitSize;
}
