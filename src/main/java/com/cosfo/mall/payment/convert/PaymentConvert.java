package com.cosfo.mall.payment.convert;

import com.cosfo.mall.order.model.dto.HuiFuPaymentRefundQueryResponseDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentRefundResponseDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.model.po.HuiFuiHostingPaymentReceive;
import com.cosfo.mall.order.model.po.HuiFuiPaymentReceive;
import com.cosfo.mall.payment.model.dto.OrderPayResultDTO;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.model.vo.PaymentResultVO;
import com.cosfo.mall.payment.model.vo.PaymentVO;
import com.cosfo.mall.wechat.bean.paymch.DirectQueryResult;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/14
 */
public class PaymentConvert {

    public static OrderPayResultDTO convertToOrderPayResultDTO(HuiFuiPaymentReceive huiFuiPaymentReceive){

        if (huiFuiPaymentReceive == null) {
            return null;
        }
        OrderPayResultDTO orderPayResultDTO = new OrderPayResultDTO();
        orderPayResultDTO.setResp_code(huiFuiPaymentReceive.getResp_code());
        orderPayResultDTO.setResp_desc(huiFuiPaymentReceive.getResp_desc());
        orderPayResultDTO.setReq_date(huiFuiPaymentReceive.getReq_date());
        orderPayResultDTO.setReq_seq_id(huiFuiPaymentReceive.getReq_seq_id());
        orderPayResultDTO.setHf_seq_id(huiFuiPaymentReceive.getHf_seq_id());
        orderPayResultDTO.setTrade_type(huiFuiPaymentReceive.getTrade_type());
        orderPayResultDTO.setTrans_type(huiFuiPaymentReceive.getTrans_type());
        orderPayResultDTO.setTrans_amt(huiFuiPaymentReceive.getTrans_amt());
        orderPayResultDTO.setTrans_stat(huiFuiPaymentReceive.getTrans_stat());
        orderPayResultDTO.setHuifu_id(huiFuiPaymentReceive.getHuifu_id());
        orderPayResultDTO.setBank_code(huiFuiPaymentReceive.getBank_code());
        orderPayResultDTO.setBank_message(huiFuiPaymentReceive.getBank_message());
        orderPayResultDTO.setDelay_acct_flag(huiFuiPaymentReceive.getDelay_acct_flag());
        orderPayResultDTO.setPay_info(huiFuiPaymentReceive.getPay_info());
        orderPayResultDTO.setQr_code(huiFuiPaymentReceive.getQr_code());
        orderPayResultDTO.setAlipay_response(huiFuiPaymentReceive.getAlipay_response());
        orderPayResultDTO.setWx_response(huiFuiPaymentReceive.getWx_response());
        orderPayResultDTO.setUnionpay_response(huiFuiPaymentReceive.getUnionpay_response());
        orderPayResultDTO.setRemark(huiFuiPaymentReceive.getRemark());
        orderPayResultDTO.setAcct_id(huiFuiPaymentReceive.getAcct_id());
        orderPayResultDTO.setSettlement_amt(huiFuiPaymentReceive.getSettlement_amt());
        orderPayResultDTO.setFee_flag(huiFuiPaymentReceive.getFee_flag());
        orderPayResultDTO.setFee_amount(huiFuiPaymentReceive.getFee_amount());
        orderPayResultDTO.setTrans_finsh_time(huiFuiPaymentReceive.getTrans_finsh_time());
        orderPayResultDTO.setEnd_time(huiFuiPaymentReceive.getEnd_time());
        orderPayResultDTO.setAcct_date(huiFuiPaymentReceive.getAcct_date());
        orderPayResultDTO.setOut_trans_id(huiFuiPaymentReceive.getOut_trans_id());
        orderPayResultDTO.setParty_order_id(huiFuiPaymentReceive.getParty_order_id());
        orderPayResultDTO.setDebit_flag(huiFuiPaymentReceive.getDebit_flag());
        orderPayResultDTO.setIs_div(huiFuiPaymentReceive.getIs_div());
        orderPayResultDTO.setAcct_split_bunch(huiFuiPaymentReceive.getAcct_split_bunch());
        orderPayResultDTO.setIs_delay_acct(huiFuiPaymentReceive.getIs_delay_acct());
        orderPayResultDTO.setWx_user_id(huiFuiPaymentReceive.getWx_user_id());
        orderPayResultDTO.setMer_dev_location(huiFuiPaymentReceive.getMer_dev_location());
        orderPayResultDTO.setTrans_fee_allowance_info(huiFuiPaymentReceive.getTrans_fee_allowance_info());
        orderPayResultDTO.setCombinedpay_data(huiFuiPaymentReceive.getCombinedpay_data());
        orderPayResultDTO.setCombinedpay_fee_amt(huiFuiPaymentReceive.getCombinedpay_fee_amt());
        orderPayResultDTO.setAppid(huiFuiPaymentReceive.getAppid());
        orderPayResultDTO.setMchid(huiFuiPaymentReceive.getMchid());
        orderPayResultDTO.setOutTradeNo(huiFuiPaymentReceive.getOutTradeNo());
        orderPayResultDTO.setTransactionId(huiFuiPaymentReceive.getTransactionId());
        orderPayResultDTO.setTradeType(huiFuiPaymentReceive.getTradeType());
        orderPayResultDTO.setTradeState(huiFuiPaymentReceive.getTradeState());
        orderPayResultDTO.setTradeStateDesc(huiFuiPaymentReceive.getTradeStateDesc());
        orderPayResultDTO.setBankType(huiFuiPaymentReceive.getBankType());
        orderPayResultDTO.setAttach(huiFuiPaymentReceive.getAttach());
        orderPayResultDTO.setSuccessTime(huiFuiPaymentReceive.getSuccessTime());
        orderPayResultDTO.setPayer(huiFuiPaymentReceive.getPayer());
        orderPayResultDTO.setAmount(huiFuiPaymentReceive.getAmount());
        orderPayResultDTO.setSceneInfo(huiFuiPaymentReceive.getSceneInfo());
        orderPayResultDTO.setPromotionDetail(huiFuiPaymentReceive.getPromotionDetail());
        return orderPayResultDTO;
    }

    public static OrderPayResultDTO convert2OrderPayResultDTO(DirectQueryResult directQueryResult){
        if (directQueryResult == null) {
            return null;
        }
        OrderPayResultDTO orderPayResultDTO = new OrderPayResultDTO();
        orderPayResultDTO.setAppid(directQueryResult.getAppid());
        orderPayResultDTO.setMchid(directQueryResult.getMchid());
        orderPayResultDTO.setOutTradeNo(directQueryResult.getOutTradeNo());
        orderPayResultDTO.setTransactionId(directQueryResult.getTransactionId());
        orderPayResultDTO.setTradeType(directQueryResult.getTradeType());
        orderPayResultDTO.setTradeState(directQueryResult.getTradeState());
        orderPayResultDTO.setTradeStateDesc(directQueryResult.getTradeStateDesc());
        orderPayResultDTO.setBankType(directQueryResult.getBankType());
        orderPayResultDTO.setAttach(directQueryResult.getAttach());
        orderPayResultDTO.setSuccessTime(directQueryResult.getSuccessTime());
        orderPayResultDTO.setPayer(directQueryResult.getPayer());
        orderPayResultDTO.setAmount(directQueryResult.getAmount());
        orderPayResultDTO.setSceneInfo(directQueryResult.getSceneInfo());
        orderPayResultDTO.setPromotionDetail(directQueryResult.getPromotionDetail());
        return orderPayResultDTO;
    }

    /**
     * 转化为PaymentDTO
     *
     * @param payment
     * @return
     */
    public static PaymentDTO convertToPaymentDTO(Payment payment) {
        if (payment == null) {
            return null;
        }

        PaymentDTO paymentDTO = new PaymentDTO();
        paymentDTO.setId(payment.getId());
        paymentDTO.setTenantId(payment.getTenantId());
        paymentDTO.setStoreId(payment.getStoreId());
        paymentDTO.setAccountId(payment.getAccountId());
        paymentDTO.setSpAppid(payment.getSpAppid());
        paymentDTO.setSpMchid(payment.getSpMchid());
        paymentDTO.setSupAppid(payment.getSupAppid());
        paymentDTO.setSubMchid(payment.getSubMchid());
        paymentDTO.setPaymentNo(payment.getPaymentNo());
        paymentDTO.setTotalPrice(payment.getTotalPrice());
        paymentDTO.setStatus(payment.getStatus());
        paymentDTO.setPrepayId(payment.getPrepayId());
        paymentDTO.setTransactionId(payment.getTransactionId());
        paymentDTO.setTradeType(payment.getTradeType());
        paymentDTO.setTradeState(payment.getTradeState());
        paymentDTO.setTradeStateDesc(payment.getTradeStateDesc());
        paymentDTO.setBankType(payment.getBankType());
        paymentDTO.setSpOpenid(payment.getSpOpenid());
        paymentDTO.setSubOpenid(payment.getSubOpenid());
        paymentDTO.setFinishTime(payment.getFinishTime());
        paymentDTO.setSuccessTime(payment.getSuccessTime());
        paymentDTO.setUpdateTime(payment.getUpdateTime());
        paymentDTO.setCreateTime(payment.getCreateTime());
        paymentDTO.setFeeRate(payment.getFeeRate());
        return paymentDTO;
    }

    public static HuiFuPayment convertToHuiFuPayment(HuiFuiHostingPaymentReceive huiFuiHostingPaymentReceive) {
        if (huiFuiHostingPaymentReceive == null) {
            return null;
        }
        HuiFuPayment huiFuPayment = new HuiFuPayment();
        huiFuPayment.setRespCode(huiFuiHostingPaymentReceive.getRespCode());
        huiFuPayment.setRespDesc(huiFuiHostingPaymentReceive.getRespDesc());
        huiFuPayment.setReqDate(huiFuiHostingPaymentReceive.getReqDate());
        huiFuPayment.setReqSeqId(huiFuiHostingPaymentReceive.getReqSeqId());
        huiFuPayment.setTransAmt(huiFuiHostingPaymentReceive.getTransAmt());
        huiFuPayment.setHuifuId(huiFuiHostingPaymentReceive.getHuifuId());
        huiFuPayment.setPayInfo(huiFuiHostingPaymentReceive.getPreOrderId());
        return huiFuPayment;
    }

    public static HuiFuPayment convertToHuiFuPayment(HuiFuiPaymentReceive huiFuPaymentReceive) {
        if (huiFuPaymentReceive == null) {
            return null;
        }
        HuiFuPayment huiFuPayment = new HuiFuPayment();
        huiFuPayment.setRespCode(huiFuPaymentReceive.getResp_code());
        huiFuPayment.setRespDesc(huiFuPaymentReceive.getResp_desc());
        huiFuPayment.setReqDate(huiFuPaymentReceive.getReq_date());
        huiFuPayment.setReqSeqId(huiFuPaymentReceive.getReq_seq_id());
        huiFuPayment.setHfSeqId(huiFuPaymentReceive.getHf_seq_id());
        huiFuPayment.setTradeType(huiFuPaymentReceive.getTrade_type());
        huiFuPayment.setTransAmt(huiFuPaymentReceive.getTrans_amt());
        huiFuPayment.setTransStat(huiFuPaymentReceive.getTrans_stat());
        huiFuPayment.setHuifuId(huiFuPaymentReceive.getHuifu_id());
        huiFuPayment.setBankCode(huiFuPaymentReceive.getBank_code());
        huiFuPayment.setBankMessage(huiFuPaymentReceive.getBank_message());
        huiFuPayment.setDelayAcctFlag(huiFuPaymentReceive.getDelay_acct_flag());
        huiFuPayment.setPayInfo(huiFuPaymentReceive.getPay_info());
        huiFuPayment.setQrCode(huiFuPaymentReceive.getQr_code());
        huiFuPayment.setAlipayResponse(huiFuPaymentReceive.getAlipay_response());
        huiFuPayment.setWxResponse(huiFuPaymentReceive.getWx_response());
        huiFuPayment.setUnionpayResponse(huiFuPaymentReceive.getUnionpay_response());
        huiFuPayment.setRemark(huiFuPaymentReceive.getRemark());
        huiFuPayment.setAcctId(huiFuPaymentReceive.getAcct_id());
        return huiFuPayment;
    }

    public static HuiFuPaymentRefundResponseDTO convertToHuiFuRefundNotifyDto(HuiFuPaymentRefundQueryResponseDTO huiFuPaymentRefundQueryResponseDTO) {
        HuiFuPaymentRefundResponseDTO huiFuPaymentRefundResponseDTO = new HuiFuPaymentRefundResponseDTO();
        huiFuPaymentRefundResponseDTO.setReq_date(huiFuPaymentRefundQueryResponseDTO.getOrg_req_date());
        huiFuPaymentRefundResponseDTO.setReq_seq_id(huiFuPaymentRefundQueryResponseDTO.getOrg_req_seq_id());
        huiFuPaymentRefundResponseDTO.setTrans_stat(huiFuPaymentRefundQueryResponseDTO.getTrans_stat());
        huiFuPaymentRefundResponseDTO.setHuifu_id(huiFuPaymentRefundQueryResponseDTO.getHuifu_id());
        huiFuPaymentRefundResponseDTO.setResp_code(huiFuPaymentRefundQueryResponseDTO.getResp_code());
        huiFuPaymentRefundResponseDTO.setResp_desc(huiFuPaymentRefundQueryResponseDTO.getResp_desc());
        huiFuPaymentRefundResponseDTO.setHf_seq_id(huiFuPaymentRefundQueryResponseDTO.getOrg_hf_seq_id());
        huiFuPaymentRefundResponseDTO.setFee_amt(huiFuPaymentRefundQueryResponseDTO.getFee_amt());
        return huiFuPaymentRefundResponseDTO;
    }

    public static PaymentRequest convertToRequest(PaymentVO paymentVO) {

        if (paymentVO == null) {
            return null;
        }
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setOrderNos(paymentVO.getOrderNos());
        paymentRequest.setPayType(paymentVO.getPayType());
        paymentRequest.setPayTypes(paymentVO.getPayTypes());
        paymentRequest.setOnlinePayChannel(paymentVO.getOnlinePayChannel());
        paymentRequest.setH5Request(paymentVO.getH5Request());
        paymentRequest.setPaymentReceipt (paymentVO.getPaymentReceipt());
        return paymentRequest;
    }

    public static PaymentResultVO convertToResultVO(PaymentResult result) {

        if (result == null) {
            return null;
        }
        PaymentResultVO paymentResultVO = new PaymentResultVO();
        paymentResultVO.setPrepayId(result.getPrepayId());
        paymentResultVO.setTimeStamp(result.getTimeStamp());
        paymentResultVO.setNonceStr(result.getNonceStr());
        paymentResultVO.setPackageStr(result.getPackageStr());
        paymentResultVO.setSignType(result.getSignType());
        paymentResultVO.setPaySign(result.getPaySign());
        paymentResultVO.setOaAppId(result.getOaAppId());
        paymentResultVO.setQrCode(result.getQrCode());
        paymentResultVO.setOrderNos(result.getOrderNos());
        paymentResultVO.setTransAmt(result.getTransAmt());
        paymentResultVO.setPayTypeFlag(result.getPayTypeFlag());
        paymentResultVO.setPaymentReceipt (result.getPaymentReceipt());
        return paymentResultVO;
    }
}
