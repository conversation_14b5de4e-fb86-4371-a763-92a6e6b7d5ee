package com.cosfo.mall.payment.template.nativepay;

import com.cosfo.mall.common.ErrorCodeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceChangeRecordTypeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceEnums;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceChangeRecordService;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceService;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.model.result.RefundExecuteResult;
import com.cosfo.mall.tenant.service.TenantPrepaymentService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @description: 账期退款
 * @author: George
 * @date: 2023-09-08
 **/
@Service
@Slf4j
public class BalanceRefund extends NativeRefund {

    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;
    @Resource
    private MerchantStoreBalanceChangeRecordService merchantStoreBalanceChangeRecordService;
    @Resource
    private TenantPrepaymentService tenantPrepaymentService;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;

    @Override
    protected RefundExecuteResult processRefund(RefundExecuteRequest request) {
        Refund refund = request.getRefund();
        OrderAfterSaleResp orderAfterSale = orderAfterSaleService.queryById(refund.getAfterSaleId());
        request.setOrderAfterSale(orderAfterSale);

        // 手动开启事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            // 余额退款
            processBalanceRefund(request);
            // 预付退款
            processThreePartiesOrderPrepaymentRefund(request);
            // 提交事务
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
        return RefundExecuteResult.builder().isSuccess(true).build();
    }

    /**
     * 处理预付的退款
     *
     * @param request
     */
    private void processThreePartiesOrderPrepaymentRefund(RefundExecuteRequest request) {
        OrderAfterSaleResp orderAfterSale = request.getOrderAfterSale();
        tenantPrepaymentService.dealThreePartiesOrderPrepaymentRefund(orderAfterSale);
    }

    /**
     * 处理余额的退款
     *
     * @param request
     */
    private void processBalanceRefund(RefundExecuteRequest request) {
        Refund refund = request.getRefund();
        Long tenantId = refund.getTenantId();
        BigDecimal refundPrice = refund.getRefundPrice();
        OrderAfterSaleResp orderAfterSale = request.getOrderAfterSale();
        Long storeId = orderAfterSale.getStoreId();
        String orderAfterSaleNo = orderAfterSale.getAfterSaleOrderNo();
        MerchantStoreBalance merchantStoreBalance = merchantStoreBalanceService.queryCashAccountByStoreId(tenantId, storeId);
        if (Objects.isNull(merchantStoreBalance)) {
            throw new ProviderException("未查询到余额账户");
        }
        int result = merchantStoreBalanceService.increaseBalance(merchantStoreBalance.getId(), refundPrice);
        if (result != 1) {
            throw new ProviderException("余额变更失败", ErrorCodeEnum.MERCHANT_STORE_BALANCE_CHANGE_FAIL);
        }
        log.info("售后单号:{} 退款成功, 退款金额: {}, 账户余额变更成功, 账户id: {}", orderAfterSaleNo, refundPrice, merchantStoreBalance.getId());

        // 余额变更记录
        MerchantStoreBalanceChangeRecord record = new MerchantStoreBalanceChangeRecord();
        record.setTenantId(tenantId);
        record.setStoreId(storeId);
        record.setChangeBalance(refundPrice);
        record.setAfterChangeBalance(merchantStoreBalance.getBalance().add(refundPrice));
        record.setAssociatedOrderNo(orderAfterSaleNo);
        record.setType(MerchantStoreBalanceChangeRecordTypeEnum.REFUND.getType());
        merchantStoreBalanceChangeRecordService.batchInsertChangeRecord(Collections.singletonList(record));
    }
}
