package com.cosfo.mall.payment.template.nativepay;

import com.cosfo.mall.common.constants.BillSwitchEnum;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckBiz;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 账期支付
 * @author: George
 * @date: 2023-08-29
 **/
@Slf4j
@Service
public class BillPayment extends NativePayment {

    @Resource
    private MerchantStoreService merchantStoreService;

    @Override
    protected void preProcess(PaymentRequest paymentRequest) {
        super.preProcess(paymentRequest);

        // 查询门店是否有账期支付权限
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        MerchantStoreDTO merchantStoreDTO = merchantStoreService.queryStoreDtoInfo(loginContextInfoDTO.getStoreId());
        AssertCheckBiz.isTrue(BillSwitchEnum.OPEN.getCode().equals(merchantStoreDTO.getBillSwitch()), ResultDTOEnum.BILL_SWITCH.getCode(), ResultDTOEnum.BILL_SWITCH.getMessage());
    }
}
