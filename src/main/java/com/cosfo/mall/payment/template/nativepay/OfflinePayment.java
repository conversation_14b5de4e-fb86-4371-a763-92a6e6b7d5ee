package com.cosfo.mall.payment.template.nativepay;

import com.cosfo.mall.common.constants.EnableOfflinePaymentEnum;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckBiz;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * @description: 线下支付
 **/
@Slf4j
@Service
public class OfflinePayment extends NativePayment {

    @Resource
    private MerchantStoreService merchantStoreService;

    @Override
    protected void preProcess(PaymentRequest paymentRequest) {
        String paymentReceipt = paymentRequest.getPaymentReceipt ();
        AssertCheckBiz.isTrue(!StringUtils.isBlank ( paymentReceipt), ResultDTOEnum.ENABLE_OFFLINE_PAYMENT_NO_PAYMENTRECEIPT.getCode(), ResultDTOEnum.ENABLE_OFFLINE_PAYMENT_NO_PAYMENTRECEIPT.getMessage());

        super.preProcess(paymentRequest);

        // 查询门店是否有支付权限
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        MerchantStoreDTO merchantStoreDTO = merchantStoreService.queryStoreDtoInfo(loginContextInfoDTO.getStoreId());
        AssertCheckBiz.isTrue(EnableOfflinePaymentEnum.OPEN.getType ().equals(merchantStoreDTO.getEnableOfflinePayment ()), ResultDTOEnum.ENABLE_OFFLINE_PAYMENT.getCode(), ResultDTOEnum.ENABLE_OFFLINE_PAYMENT.getMessage());
    }
}
