package com.cosfo.mall.payment.template.huifu.plugin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.config.BusinessTimeConfig;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constants.PayTypeFlagEnum;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.model.dto.HuiFuDTO;
import com.cosfo.mall.common.utils.SignatureUtil;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.dto.HuiFuHostingPaymentDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentQueryRequestDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.model.po.HuiFuiHostingPaymentReceive;
import com.cosfo.mall.order.model.po.HuiFuiPaymentReceive;
import com.cosfo.mall.payment.convert.PaymentConvert;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.dto.OrderPayResultDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.template.huifu.HuiFuPaymentTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.mall.wechat.api.HuiFuApi;
import com.cosfo.mall.wechat.bean.base.Amount;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-09-01
 **/
@Service
@Slf4j
public class HuiFuWechatPluginPayment extends HuiFuPaymentTemplate {

    @Resource
    private TenantService tenantService;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    private HuiFuConfig huiFuConfig;
    @Value("${notify-domain}")
    private String notifyDomain;
    @Resource
    private BusinessTimeConfig businessTimeConfig;

    @Override
    protected void preProcess(PaymentRequest request) {
        super.preProcess(request);
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(loginContextInfoDTO.getTenantId());
        Integer wechatIndirectPluginSwitch = tenantAuthConnectionDTO.getWechatIndirectPluginSwitch();
        if (PaymentEnum.Switch.CLOSE.getType().equals(wechatIndirectPluginSwitch)) {
            throw new BizException("您暂无微信支付权限");
        }
    }

    @Override
    protected PaymentResult processPay(PaymentRequest request) {
        // 封装调用汇付api所需数据
        HuiFuDTO huiFuDTO = assemblyHuiFuPayRequest(request);
        String result = HuiFuApi.huiFuHostingPay(huiFuDTO);
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(result, HuiFuDTO.class);
        HuiFuiHostingPaymentReceive huiFuiHostingPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuiHostingPaymentReceive.class);

        //插入汇付支付信息
        com.cosfo.mall.order.model.po.HuiFuPayment huiFuPayment = PaymentConvert.convertToHuiFuPayment(huiFuiHostingPaymentReceive);
        huiFuPayment.setTenantId(request.getTenantId());
        huiFuPayment.setPaymentId(request.getPaymentId());
        if (request.isCombineRequest()) {
            huiFuPayment.setPaymentId(request.getMasterPaymentId());
        }
        huiFuPaymentMapper.insert(huiFuPayment);

        if (!Objects.equals(HuiFuApi.PLUGIN_PAY_SUCCESS_CODE, huiFuPayment.getRespCode())) {
            log.error("发起汇付支付失败，失败原因：{},响应码：{} ", huiFuPayment.getRespDesc(), huiFuPayment.getRespCode());
            throw new ProviderException("本次支付交易失败, 请稍后重试");
        }

        // 返回前端拉起支付数据
        PaymentResult paymentResult = new PaymentResult();
        paymentResult.setSuccess(true);
        paymentResult.setPrepayId(huiFuPayment.getPayInfo());
        paymentResult.setPayTypeFlag(PayTypeFlagEnum.APPLET_HUI_FU_PLUGIN_FLAG.getCode());
        return paymentResult;
    }


    @Override
    public PaymentResult queryLastPaymentResult(PaymentRequest paymentRequest) {
        Long paymentId = paymentRequest.getPaymentId();
        Long tenantId = paymentRequest.getTenantId();
        com.cosfo.mall.order.model.po.HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>().eq(com.cosfo.mall.order.model.po.HuiFuPayment::getPaymentId, paymentId));
        if (huiFuPayment == null) {
            return null;
        }

        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.selectByTenantId(tenantId);

        HuiFuPaymentQueryRequestDTO huiFuPaymentQueryRequestDTO = new HuiFuPaymentQueryRequestDTO();
        huiFuPaymentQueryRequestDTO.setOrgReqDate(huiFuPayment.getReqDate());
        huiFuPaymentQueryRequestDTO.setHuiFuId(huiFuPayment.getHuifuId());
        huiFuPaymentQueryRequestDTO.setOrgHfSeqId(huiFuPayment.getHfSeqId());
        if (StringUtils.isEmpty(huiFuPaymentQueryRequestDTO.getOrgHfSeqId())) {
            huiFuPaymentQueryRequestDTO.setOrgReqSeqId(huiFuPayment.getReqSeqId());
        }
        HuiFuDTO<HuiFuPaymentQueryRequestDTO> huiFuDTO = new HuiFuDTO<>(huiFuPaymentQueryRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(huiFuPayment.getHuifuId());
        log.info("汇付支付查询请求参数：{}", JSONObject.toJSONString(huiFuDTO));
        String result = HuiFuApi.queryPaymentResult(huiFuDTO);
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(result, HuiFuDTO.class);
        HuiFuiPaymentReceive huiFuPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuiPaymentReceive.class);

        OrderPayResultDTO orderPayResultDTO = PaymentConvert.convertToOrderPayResultDTO(huiFuPaymentReceive);
        orderPayResultDTO.setPaymentId(paymentId);
        if (Objects.nonNull(orderPayResultDTO) && Objects.nonNull(orderPayResultDTO.getTrans_stat())) {
            orderPayResultDTO.setPaymentStatus(PaymentEnum.Status.getStatusByHuifuStat(orderPayResultDTO.getTrans_stat()).getCode());
        }
        PaymentResult paymentResult = new PaymentResult();
        paymentResult.setOrderPayResultDTO(orderPayResultDTO);
        if (Constants.HUIFU_REQ_ID_NOT_EXIST.equals(orderPayResultDTO.getResp_code())) {
            return null;
        }
        return paymentResult;
    }

    /**
     * 组装汇付支付请求的参数
     *
     * @param request
     * @return
     */
    private HuiFuDTO assemblyHuiFuPayRequest(PaymentRequest request) {
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.selectByTenantId(request.getTenantId());
        HuiFuHostingPaymentDTO huiFuHostingPaymentDTO = new HuiFuHostingPaymentDTO();
        huiFuHostingPaymentDTO.setPreOrderType("3");
        huiFuHostingPaymentDTO.setReqDate(TimeUtils.changeDate2String(new Date(), Constants.HUIFU_DATE));
        huiFuHostingPaymentDTO.setReqSeqId(request.getPaymentNo());
        huiFuHostingPaymentDTO.setHuifuId(tenantAuthConnection.getHuifuId());

        Long paymentId = request.getPaymentId();
        if (request.isCombineRequest()) {
            paymentId = request.getMasterPaymentId();
        }
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        Amount amount = new Amount();
        amount.setTotalPrice(request.getTransAmt());
        // 交易金额
        String transAmt = String.valueOf(amount.getTotalPrice().setScale(2, RoundingMode.HALF_UP));
        huiFuHostingPaymentDTO.setTransAmt(transAmt);
        huiFuHostingPaymentDTO.setMiniappData("{}");
        huiFuHostingPaymentDTO.setGoodsDesc(StringUtils.isNotBlank(request.getPaymentDesc()) ? request.getPaymentDesc() : TimeUtils.changeDate2String(new Date(), Constants.HUIFU_EXPIRE_DATE) + Constants.HUIFU_PRODUCTS_DESC);
        // 是否延时交易
        huiFuHostingPaymentDTO.setDelayAcctFlag("Y");
        long paymentExpireTime = businessTimeConfig.getPaymentExpireTime();
        log.info("支付单：[{}]支付过期时间为：[{}]分钟", request.getPaymentNo(), paymentExpireTime);
        LocalDateTime payDeadline = payment.getCreateTime().plusMinutes(paymentExpireTime);
        String expireTime = payDeadline.format(DateTimeFormatter.ofPattern(Constants.HUIFU_EXPIRE_DATE));
        huiFuHostingPaymentDTO.setTimeExpire(expireTime);
        huiFuHostingPaymentDTO.setNotifyUrl(notifyDomain + "/pay-notify/huifu-pay");

        //参数加签
        String privateKey = tenantAuthConnection.getSecretKey();
        String signRes = SignatureUtil.bodySign(huiFuHostingPaymentDTO, privateKey);

        //请求汇付支付
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuHostingPaymentDTO);
        huiFuDTO.setSys_id(huiFuHostingPaymentDTO.getHuifuId());
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSign(signRes);
        return huiFuDTO;
    }
}
