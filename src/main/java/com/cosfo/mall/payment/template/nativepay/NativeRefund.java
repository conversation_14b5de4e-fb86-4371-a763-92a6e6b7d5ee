package com.cosfo.mall.payment.template.nativepay;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.context.OutRefundResultEnum;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.model.request.RefundRequest;
import com.cosfo.mall.payment.model.result.RefundExecuteResult;
import com.cosfo.mall.payment.template.RefundTemplate;
import com.cosfo.mall.payment.utils.RefundComponent;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @description: 本地支付退款
 * @author: George
 * @date: 2023-09-08
 **/
@Slf4j
@Service
public abstract class NativeRefund extends RefundTemplate {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;
    @Resource
    private RefundComponent refundComponent;

    /**
     * 生成退款单
     *
     * @param request
     * @return
     */
    protected Long createRefundOrder(RefundRequest request) {
        PaymentItem item = paymentItemMapper.selectPaySuccessByOrderId(request.getTenantId(), request.getOrderId());
        Payment payment = null;
        if (item != null) {
            payment = paymentMapper.selectByPrimaryKey(item.getPaymentId());
        }

        String refundNo = Global.generateRefundNo();
        Refund refund = new Refund();
        refund.setTenantId(request.getTenantId());
        refund.setAfterSaleId(request.getOrderAfterSaleId());
        refund.setRefundNo(refundNo);
        refund.setPaymentPrice(Objects.isNull(payment) ? null : payment.getTotalPrice());
        refund.setRefundStatus(RefundEnum.Status.CREATE_REFUND.getStatus());
        refund.setRefundPrice(request.getRefundPrice());
        refund.setCreateTime(LocalDateTime.now());
        refund.setPaymentId(Objects.isNull(payment) ? null : payment.getId());
        refundMapper.insertSelective(refund);
        return refund.getId();

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onSuccess(RefundExecuteRequest request, RefundExecuteResult result) {
        OrderAfterSaleResp orderAfterSale = request.getOrderAfterSale();
        Refund refund = request.getRefund();

        // 退款成功后的操作
        orderAfterSaleService.payRefundSuccessDeal(orderAfterSale.getId());

        // 费用明细记录
        refundComponent.generateOrderItemFee(orderAfterSale);

        // 更新Refund状态为已成功
        int updateStatusCas = refundMapper.updateStatusCas(refund.getId(), RefundEnum.Status.IN_REFUND.getStatus(), RefundEnum.Status.SUCCESS.getStatus());
        if (updateStatusCas <= 0) {
            log.error("退款重试定时任务 乐观更新为退款成功状态失败本次不处理,refund:{}", JSON.toJSONString(refund));
            throw new ProviderException("乐观更新为退款成功失败，暂不退款");
        }
    }

    @Override
    protected void onFailure(RefundExecuteRequest request, RefundExecuteResult result) {

    }

    @Override
    protected OutRefundResultEnum doLastRefundResult(RefundExecuteRequest request) {
        return null;
    }
}
