package com.cosfo.mall.payment.template.huifu.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constants.PayTypeEnum;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.template.huifu.HuiFuPaymentTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-09-01
 **/
@Service
@Slf4j
public class HuiFuWechatPayment extends HuiFuPaymentTemplate {

    @Resource
    private TenantService tenantService;
    @Resource
    private PaymentMapper paymentMapper;

    @Override
    protected void preProcess(PaymentRequest request) {
        super.preProcess(request);
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(loginContextInfoDTO.getTenantId());
        // 小程序汇付微信校验wechatIndirectSwitch
        if (!request.getH5Request()) {
            Integer wechatIndirectSwitch = tenantAuthConnectionDTO.getWechatIndirectSwitch();
            if (PaymentEnum.Switch.CLOSE.getType().equals(wechatIndirectSwitch)) {
                throw new BizException("您暂无微信支付权限");
            }
        }

        // 如果是H5平台，检查公众号配置是否完整
        if (request.getH5Request()) {
            Integer h5WechatIndirectSwitch = tenantAuthConnectionDTO.getH5WechatIndirectSwitch();
            if (PaymentEnum.Switch.CLOSE.getType().equals(h5WechatIndirectSwitch)) {
                throw new BizException("您暂无微信支付权限");
            }
            if (StringUtils.isEmpty(tenantAuthConnectionDTO.getOaAppId()) || StringUtils.isEmpty(tenantAuthConnectionDTO.getOaAppSecret())) {
                throw new BizException("公众号信息未配置，请联系管理员");
            }
        }
    }

    @Override
    protected PaymentResult assemblyPaymentResult(PaymentRequest request, PaymentResult result) {
        super.assemblyPaymentResult(request, result);
        String prepayId = result.getPrepayId();
        JSONObject jsonObject = JSON.parseObject(prepayId);
        result.setTimeStamp(jsonObject.getString("timeStamp"));
        result.setNonceStr(jsonObject.getString("nonceStr"));
        result.setPackageStr(jsonObject.getString("package"));
        result.setSignType(jsonObject.getString("signType"));
        result.setPaySign(jsonObject.getString("paySign"));
        result.setOaAppId(jsonObject.getString("appId"));
        return result;
    }

    /**
     * 支付成功操作
     *
     * @param request 支付成功对象
     * @param result  支付结果对象
     */
    @Override
    protected void onSuccess(PaymentRequest request, PaymentResult result) {
        Long paymentId = request.getPaymentId();
        Payment payment = new Payment();
        payment.setId(paymentId);
        payment.setPrepayId(result.getPrepayId());
        paymentMapper.updateByPrimaryKeySelective(payment);

        super.onSuccess(request, result);
    }
}
