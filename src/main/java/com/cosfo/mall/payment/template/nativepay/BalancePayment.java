package com.cosfo.mall.payment.template.nativepay;


import com.cosfo.mall.common.ErrorCodeEnum;
import com.cosfo.mall.common.constants.BillSwitchEnum;
import com.cosfo.mall.common.constants.PayTypeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceChangeRecordTypeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceEnums;
import com.cosfo.mall.common.exception.PayBizException;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckBiz;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceChangeRecordService;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceService;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.cosfo.mall.order.model.bo.OrderBalanceBO;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * @description: 余额支付
 * @author: George
 * @date: 2023-08-29
 **/
@Slf4j
@Service
public class BalancePayment extends NativePayment {

    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;
    @Resource
    private MerchantStoreBalanceChangeRecordService merchantStoreBalanceChangeRecordService;

    @Override
    protected void preProcess(PaymentRequest paymentRequest) {
        super.preProcess(paymentRequest);

        // 查询门店是否有余额支付权限
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        MerchantStoreDTO merchantStoreDTO = merchantStoreService.queryStoreDtoInfo(loginContextInfoDTO.getStoreId());
        AssertCheckBiz.isTrue(BillSwitchEnum.OPEN.getCode().equals(merchantStoreDTO.getBalanceAuthority()), ResultDTOEnum.BALANCE_SWITCH.getCode(), ResultDTOEnum.BALANCE_SWITCH.getMessage());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrderPaymentTransaction(OrderBalanceBO orderBalanceBO) {
        // 余额支付时,还要再扣除余额
        decreaseOrderBalance(orderBalanceBO);

        super.processOrderPaymentTransaction(orderBalanceBO);
    }

    private void decreaseOrderBalance(OrderBalanceBO orderBalanceBO) {
        Order order = orderBalanceBO.getOrder();
        BigDecimal needPayPrice = orderBalanceBO.getNeedPayPrice();
        String needPayPriceString = needPayPrice.setScale(2, RoundingMode.DOWN).toPlainString();
        BigDecimal totalPrice = order.getPayablePrice();
        // 查询门店余额
        MerchantStoreBalance merchantStoreBalance = merchantStoreBalanceService.queryCashAccountByStoreId(order.getTenantId(), order.getStoreId());
        if (Objects.isNull(merchantStoreBalance)) {
            throw new PayBizException("当前余额，不足以覆盖订单金额，需现结支付" + needPayPriceString + "元");
        }
        BigDecimal balance = merchantStoreBalance.getBalance();
        // 余额不足自营金额
        if (balance.compareTo(totalPrice) < 0) {
            throw new PayBizException("当前余额，不足以覆盖订单金额，需现结支付" + needPayPriceString + "元");
        }

        Integer decreaseType = orderBalanceBO.getDecreaseType();
        if (Objects.equals(decreaseType, MerchantStoreBalanceEnums.DecreaseTypeEnum.DECREASE.getType())) {
            // 扣减余额
            int result = merchantStoreBalanceService.decreaseBalance(merchantStoreBalance.getId(), totalPrice);
            if (result != 1) {
                throw new PayBizException("当前余额，不足以覆盖订单金额，需现结支付" + needPayPriceString + "元");
            }
            log.info("订单[{}]余额支付成功, 扣减金额[{}]", order.getId(), totalPrice);
            // 余额消费记录
            merchantStoreBalanceChangeRecordService.generateBalanceChangeRecordByOrder(order.getOrderNo(), totalPrice, MerchantStoreBalanceChangeRecordTypeEnum.CONSUMPTION.getType(), merchantStoreBalance);
        }

        if (Objects.equals(decreaseType, MerchantStoreBalanceEnums.DecreaseTypeEnum.FREEZE.getType())) {
            // 冻结余额
            int result = merchantStoreBalanceService.freezeBalance(merchantStoreBalance.getId(), totalPrice);
            if (result != 1) {
                throw new PayBizException("当前余额，不足以覆盖订单金额，需现结支付" + needPayPriceString + "元");
            }
            log.info("订单[{}]余额支付成功, 冻结金额[{}]", order.getId(), totalPrice);
            merchantStoreBalanceChangeRecordService.generateBalanceChangeRecordByOrder(order.getOrderNo(), totalPrice, MerchantStoreBalanceChangeRecordTypeEnum.FROZEN.getType(), merchantStoreBalance);
        }
    }
}
