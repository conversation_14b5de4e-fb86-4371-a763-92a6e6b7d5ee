package com.cosfo.mall.payment.template.wechat;

import cn.hutool.core.math.Money;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.PemUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import com.cosfo.mall.common.ErrorCodeEnum;
import com.cosfo.mall.common.config.BusinessTimeConfig;
import com.cosfo.mall.common.config.FanTaiPaymentConfig;
import com.cosfo.mall.common.constants.WechatTradeTypeEnums;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.payment.convert.PaymentConvert;
import com.cosfo.mall.payment.mapper.PaymentCombinedDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.dto.OrderPayResultDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.request.WechatPaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.template.PayTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.mall.wechat.api.PayMchAPI;
import com.cosfo.mall.wechat.bean.base.Amount;
import com.cosfo.mall.wechat.bean.base.Payer;
import com.cosfo.mall.wechat.bean.paymch.DirectQueryResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.security.PrivateKey;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Objects;

/**
 * @description: 微信支付
 * @author: George
 * @date: 2023-08-29
 **/
@Slf4j
@Service
public class WechatPayment extends PayTemplate {

    @Resource
    private TenantService tenantService;
    @Resource
    private FanTaiPaymentConfig fanTaiPaymentConfig;
    @Resource
    private PaymentService paymentService;
    @Resource
    private PaymentMapper paymentMapper;
    @Value("${notify-domain}")
    private String notifyDomain;
    @Resource
    private BusinessTimeConfig businessTimeConfig;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;


    @Override
    protected void preProcess(PaymentRequest request) {
        super.preProcess(request);
        // 微信开关校验
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(loginContextInfoDTO.getTenantId());
        Integer wechatDirectSwitch = tenantAuthConnectionDTO.getWechatDirectSwitch();
        if (wechatDirectSwitch == PaymentEnum.Switch.CLOSE.getType()) {
            throw new BizException("您暂无微信支付权限");
        }
        // 校验微信商户配置信息是否完整
        if (StringUtils.isEmpty(tenantAuthConnectionDTO.getPayMchid()) ||
                StringUtils.isEmpty(tenantAuthConnectionDTO.getPaySecret()) ||
                StringUtils.isEmpty(tenantAuthConnectionDTO.getPayCertPath())) {
            throw new BizException("微信商户信息未配置，请联系管理员");
        }
    }

    @Override
    protected PaymentResult processPay(PaymentRequest request) {
        // 组装调用微信支付接口需要的参数
        WechatPaymentRequest wechatPaymentRequest = assemblyWechatPayRequest(request);
        // 调用微信接口
        String result = PayMchAPI.jsapiPay(wechatPaymentRequest, request.getWechatPrivateKey(), request.getPayCertPath());
        // 返回支付信息
        PaymentResult paymentResult = new PaymentResult();
        paymentResult.setPrepayId(result);
        paymentResult.setSuccess(true);
        return paymentResult;
    }

    /**
     * 组装调用微信请求的参数
     *
     * @param request
     * @return
     */
    private WechatPaymentRequest assemblyWechatPayRequest(PaymentRequest request) {
        WechatPaymentRequest wechatPaymentRequest = new WechatPaymentRequest();
        wechatPaymentRequest.setAppid(request.getSpAppid());
        wechatPaymentRequest.setMchid(request.getSpMchid());
        wechatPaymentRequest.setOut_trade_no(request.getPaymentNo());
        wechatPaymentRequest.setNotify_url(notifyDomain + "/pay-notify/wx-direct");
        //改成实际的商品名称
        wechatPaymentRequest.setDescription(StringUtils.isNotBlank(request.getPaymentDesc()) ? request.getPaymentDesc() : "帆台商品");
        //可支付时间
        Long paymentId = request.getPaymentId();
        if (request.isCombineRequest()) {
            paymentId = request.getMasterPaymentId();
        }
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        long paymentExpireTime = businessTimeConfig.getPaymentExpireTime();
        log.info("支付单：[{}]支付过期时间为：[{}]分钟", request.getPaymentNo(), paymentExpireTime);
        LocalDateTime payDeadline = payment.getCreateTime().plusMinutes(paymentExpireTime);
        if (payDeadline.isBefore(LocalDateTime.now())) {
            throw new BizException("订单支付已超时，请重新下单");
        }
        String format = payDeadline.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss+08:00"));
        wechatPaymentRequest.setTime_expire(format);

        //金额信息
        Amount amount = new Amount();
        long cent = new Money(request.getTransAmt()).getCent();
        amount.setTotal((int) cent);
        wechatPaymentRequest.setAmount(amount);

        //支付人信息
        Payer payer = new Payer();
        payer.setOpenid(payment.getSpOpenid());
        wechatPaymentRequest.setPayer(payer);

        // 结算分账信息
//        if (ProfitSharingSwitchEnum.OPEN.getCode().equals(tenant.getProfitSharingSwitch())) {
//            SettleInfo settleInfo = new SettleInfo();
//            settleInfo.setProfit_sharing(Boolean.TRUE);
//            wechatPaymentRequest.setSettle_info(settleInfo);
//        }
        return wechatPaymentRequest;
    }

    @Override
    protected void assemblyPaymentInfo(PaymentRequest request) {
        // 调用父类组装一些公共的参数
        super.assemblyPaymentInfo(request);
        // 微信支付需要商户号等信息
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(request.getTenantId());
        request.setSpAppid(tenantAuthConnectionDTO.getAppId());
        request.setSpMchid(tenantAuthConnectionDTO.getPayMchid());
        request.setTradeType(WechatTradeTypeEnums.JSAPI.getDesc());
        //request.setProfitSharingSwitch(tenantDTO.getProfitSharingSwitch());

        // 读取微信支付证书
        PrivateKey privateKey;
        String payCertPath;
        try {
            payCertPath = tenantAuthConnectionDTO.getPayCertPath();
            privateKey = PemUtil.readPemPrivateKey(new FileInputStream(payCertPath + "/apiclient_key.pem"));
        } catch (FileNotFoundException e) {
            throw new ProviderException(ErrorCodeEnum.READ_PEM_PRIVATE_KEY);
        }
        request.setWechatPrivateKey(privateKey);
        request.setPayCertPath(payCertPath);
    }

    @Override
    protected PaymentResult assemblyPaymentResult(PaymentRequest request, PaymentResult result) {
        super.assemblyPaymentResult(request, result);
        String spAppid = request.getSpAppid();
        String prepayId = result.getPrepayId();
        PrivateKey wechatPrivateKey = request.getWechatPrivateKey();

        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonceStr = RandomUtil.randomString(32).toUpperCase();
        String signStr = spAppid + "\n" +
                timeStamp + "\n" +
                nonceStr + "\n" +
                "prepay_id=" + prepayId + "\n";

        Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA);
        sign.setPrivateKey(wechatPrivateKey);
        String paySign = Base64.getEncoder().encodeToString(sign.sign(signStr));
        result.setTimeStamp(timeStamp);
        result.setNonceStr(nonceStr);
        result.setPackageStr("prepay_id=" + prepayId);
        result.setSignType("RSA");
        result.setPaySign(paySign);
        return result;
    }

    @Override
    protected void paySuccess(PaymentRequest request, PaymentResult result) {
        // 调用原有的支付成功的操作
        OrderPayResultDTO orderPayResultDTO = result.getOrderPayResultDTO();
        paymentService.handleWXPayNotify(orderPayResultDTO);
    }

    @Override
    public boolean callClosePayRequest(PaymentRequest paymentRequest) {
        // 调用原有关单接口
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(paymentRequest.getTenantId());
        Boolean closeResult = PayMchAPI.wechatCloseJsapiPayOrder(paymentRequest.getPaymentNo(), tenantAuthConnectionDTO.getPayMchid(), tenantAuthConnectionDTO.getPayCertPath());
        if (!closeResult) {
            log.error("关闭原支付单：{}失败", paymentRequest.getPaymentId());
            throw new BizException("当前支付操作失败，请稍后重试");
        }

        return true;
    }

    @Override
    public PaymentResult queryLastPaymentResult(PaymentRequest paymentRequest) {
        DirectQueryResult directQueryResult = paymentService.queryPayResult(paymentRequest.getPaymentId());
        OrderPayResultDTO orderPayResultDTO = PaymentConvert.convert2OrderPayResultDTO(directQueryResult);
        if (Objects.nonNull(orderPayResultDTO)) {
            orderPayResultDTO.setPaymentId(paymentRequest.getPaymentId());
            orderPayResultDTO.setPaymentStatus(PaymentEnum.Status.getStatusByWechatStat(orderPayResultDTO.getTradeState()).getCode());
        }
        PaymentResult paymentResult = new PaymentResult();
        paymentResult.setOrderPayResultDTO(orderPayResultDTO);
        return paymentResult;
    }

    /**
     * 支付成功操作
     *
     * @param request 支付成功对象
     * @param result  支付结果对象
     */
    @Override
    protected void onSuccess(PaymentRequest request, PaymentResult result) {
        if (request.isCombineRequest()) {
            // 更新子支付单的状态为处理中
            Long paymentId = request.getPaymentId();
            int updateStatus = paymentCombinedDetailService.updateStatus(
                    paymentId,
                    PaymentEnum.Status.DEALING.getCode(),
                    PaymentEnum.Status.WAITING.getCode());
            if (updateStatus != 1) {
                log.error("支付单：[{}]由待支付变更程处理中异常", paymentId);
                throw new ProviderException("本次支付交易失败，请稍后再试");
            }
            return;
        }

        Long paymentId = request.getPaymentId();
        Payment payment = new Payment();
        payment.setId(paymentId);
        payment.setPrepayId(result.getPrepayId());
        paymentMapper.updateByPrimaryKeySelective(payment);

        // 乐观更新支付单为锁定状态
        int updateStatus = paymentMapper.updateStatus(paymentId, PaymentEnum.Status.DEALING.getCode(), PaymentEnum.Status.WAITING.getCode());
        if (updateStatus != 1) {
            log.error("支付单：[{}]由待支付变更成锁定中失败", paymentId);
            throw new ProviderException("本次支付交易失败，请稍后再试");
        }
    }
}
