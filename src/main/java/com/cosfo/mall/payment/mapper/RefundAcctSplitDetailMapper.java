package com.cosfo.mall.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.mall.payment.model.po.RefundAcctSplitDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/18
 */
@Mapper
public interface RefundAcctSplitDetailMapper extends BaseMapper<RefundAcctSplitDetail> {
    /**
     * 批量保存
     *
     * @param refundAcctSplitDetailList
     */
    void saveBatch(@Param("refundAcctSplitDetailList") List<RefundAcctSplitDetail> refundAcctSplitDetailList);
}
