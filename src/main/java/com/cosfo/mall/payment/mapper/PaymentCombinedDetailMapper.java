package com.cosfo.mall.payment.mapper;

import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: ${description}
 * @author: George
 * @date: 2025-04-21
 **/
@Mapper
public interface PaymentCombinedDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PaymentCombinedDetail record);

    int insertSelective(PaymentCombinedDetail record);

    PaymentCombinedDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaymentCombinedDetail record);

    int updateByPrimaryKey(PaymentCombinedDetail record);

    /**
     * 乐观锁更新状态
     *
     * @param id
     * @param currentStatus
     * @param beforeStatus
     * @return
     */
    int updateStatus(@Param("id") Long id,
                     @Param("currentStatus") Integer currentStatus,
                     @Param("beforeStatus") Integer beforeStatus);


    /**
     * 根据组合支付单号查询支付明细
     *
     * @param combinePaymentNo
     * @return
     */
    List<PaymentCombinedDetail> selectByCombinedPaymentNo(String combinePaymentNo);
}