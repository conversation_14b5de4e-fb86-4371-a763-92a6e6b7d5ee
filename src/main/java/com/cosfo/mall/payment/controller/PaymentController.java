package com.cosfo.mall.payment.controller;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.payment.convert.PaymentConvert;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.dto.PaymentQueryDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.model.vo.PaymentResultVO;
import com.cosfo.mall.payment.model.vo.PaymentVO;
import com.cosfo.mall.payment.service.PaymentService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description: 支付控制层
 * @author: George
 * @date: 2023-08-29
 **/
@RestController
@RequestMapping("/payment")
public class PaymentController {

    @Resource
    private PaymentService paymentService;

    /**
     * 支付接口
     *
     * @param paymentVO 请求对象
     * @return 支付结果
     */
    @PostMapping
    public ResultDTO pay(@RequestBody PaymentVO paymentVO) {
        PaymentRequest request = PaymentConvert.convertToRequest(paymentVO);
        PaymentResult result = paymentService.pay(request);
        PaymentResultVO paymentResultVO = PaymentConvert.convertToResultVO(result);
        return ResultDTO.success(result.getCode(), paymentResultVO);
    }

    /**
     * 查询支付单金额
     *
     * @param paymentQueryDTO
     * @return
     */
    @PostMapping(value = "/query-total-price")
    public CommonResult<String> query(@RequestBody PaymentQueryDTO paymentQueryDTO) {
        return CommonResult.ok(paymentService.queryByPartyOrderId(paymentQueryDTO.getPartyOrderId()));
    }
}
