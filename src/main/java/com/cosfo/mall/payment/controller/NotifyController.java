package com.cosfo.mall.payment.controller;

import cn.hutool.http.server.HttpServerRequest;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.mall.wechat.bean.notify.DirectNotify;
import com.cosfo.mall.wechat.bean.notify.NotifyResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022/5/23  17:32
 */
@RestController
public class NotifyController {
    @Resource
    private PaymentService paymentService;
    @Resource
    private RefundService refundService;

    @RequestMapping(value = "/pay-notify/wx-direct", method = RequestMethod.POST)
    public NotifyResponse wxDirectPayNotify(@RequestBody DirectNotify directNotify, HttpServerRequest request) {
        return paymentService.wxDirectPayNotify(directNotify, request);
    }

    /**
     * 汇付聚合正扫异步通知接口
     *
     * @param huiFuDTO
     * @return
     */
    @RequestMapping("/pay-notify/huifu-pay")
    public String huiFuPayNotification(HttpServletRequest request){
        return paymentService.huiFuPayNotification(request);
    }

    /**
     * 汇付退款回调
     * @param request 退款回调
     * @return
     */
    @RequestMapping("/pay-notify/huifu-refund")
    public String huiFuRefundNotify(HttpServletRequest request) {
        return refundService.handleHuiFuRefundNotify(request);
    }
}
