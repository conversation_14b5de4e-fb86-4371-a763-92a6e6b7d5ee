package com.cosfo.mall.payment.service.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.IdUtil;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.common.context.DecreaseTypeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceChangeRecordTypeEnum;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.context.prepay.TenantPrepayTransactionEnum;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.mall.merchant.repository.MerchantStoreBalanceChangeRecordRepository;
import com.cosfo.mall.merchant.repository.MerchantStoreBalanceRepository;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceService;
import com.cosfo.mall.payment.mapper.PaymentCombinedDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentCombinedOrderDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentCombinedOrderDetailService;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransaction;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransactionItem;
import com.cosfo.mall.tenant.service.TenantPrepaymentAccountService;
import com.cosfo.mall.tenant.service.TenantPrepaymentTransactionItemService;
import com.cosfo.mall.tenant.service.TenantPrepaymentTransactionService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2025-04-27
 **/
@Slf4j
@Service
public class PaymentCombinedDetailServiceImpl implements PaymentCombinedDetailService {

    @Resource
    private MerchantStoreBalanceRepository merchantStoreBalanceRepository;
    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;
    @Resource
    private MerchantStoreBalanceChangeRecordRepository merchantStoreBalanceChangeRecordRepository;
    @Resource
    private TenantPrepaymentTransactionItemService tenantPrepaymentTransactionItemService;
    @Resource
    private TenantPrepaymentAccountService tenantPrepaymentAccountService;
    @Resource
    private TenantPrepaymentTransactionService tenantPrepaymentTransactionService;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @Resource
    private PaymentCombinedDetailMapper paymentCombinedDetailMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentCombinedOrderDetailService paymentCombinedOrderDetailService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void decreaseFreezeAmount(PaymentCombinedDetail detail) {
        log.info("开始处理组合支付明细冻结金额实际扣减, detail: {}", detail);
        processFreezeAmount(detail, true);
        log.info("处理组合支付明细冻结金额实际扣减完毕, detail: {}", detail);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void releaseFreezeAmount(PaymentCombinedDetail detail) {
        log.info("开始处理组合支付明细冻结金额释放, detail: {}", detail);
        processFreezeAmount(detail, false);
        log.info("处理组合支付明细冻结金额释放完毕, detail: {}", detail);
    }

    private void processFreezeAmount(PaymentCombinedDetail detail, boolean isDecrease) {
        // 检查状态
        boolean legal = checkCombinedDetail(detail);
        if (!legal) {
            return;
        }

        Long tenantId = detail.getTenantId();
        Long storeId = detail.getStoreId();
        Long combinedDetailId = detail.getId();

        // 获取订单明细
        List<PaymentCombinedOrderDetail> orderDetails = paymentCombinedOrderDetailService.selectByCombinedDetailIds(tenantId, Collections.singletonList(combinedDetailId));
        if (CollectionUtils.isEmpty(orderDetails)) {
            throw new ParamsException("未查询到组合支付订单明细, combinedDetailId=" + combinedDetailId);
        }

        List<String> orderNos = orderDetails.stream()
                .map(PaymentCombinedOrderDetail::getOrderNo)
                .collect(Collectors.toList());

        // 处理门店余额
        List<MerchantStoreBalanceChangeRecord> balanceChangeRecords = merchantStoreBalanceChangeRecordRepository.queryByAssociatedOrderNos(orderNos);
        long count = balanceChangeRecords.stream().filter(record -> !Objects.equals(record.getType(), MerchantStoreBalanceChangeRecordTypeEnum.FROZEN.getType())).count();
        if (count > 0) {
            log.error("门店余额冻结记录异常，records:{}", balanceChangeRecords, new ProviderException(String.format("门店余额冻结记录异常，单号%s", orderNos)));
            return;
        }
        if (!CollectionUtils.isEmpty(balanceChangeRecords)) {
            processMerchantStoreBalance(tenantId, storeId, balanceChangeRecords, isDecrease);
        }

        // 处理预付余额
        processPrepaymentBalance(tenantId, orderDetails, orderNos, isDecrease);

        // 更新组合支付明细状态
        updateCombinedDetailStatus(combinedDetailId, isDecrease ? PaymentEnum.Status.SUCCESS : PaymentEnum.Status.CANCELED);
    }

    private void processMerchantStoreBalance(Long tenantId, Long storeId,
                                             List<MerchantStoreBalanceChangeRecord> records, boolean isDecrease) {
        for (MerchantStoreBalanceChangeRecord record : records) {
            MerchantStoreBalance storeBalance = merchantStoreBalanceRepository.queryByStoreAndFundAccountId(
                    tenantId, storeId, record.getFundAccountId()
            );
            if (storeBalance == null) {
                throw new ProviderException(String.format(
                        "未找到门店余额记录, tenantId=%d, storeId=%d, fundAccountId=%s",
                        tenantId, storeId, record.getFundAccountId()
                ));
            }

            // 处理余额
            int updated = isDecrease
                    ? merchantStoreBalanceService.decreaseFreezeBalance(storeBalance.getId(), record.getChangeBalance().negate())
                    : merchantStoreBalanceService.freezeBalance(storeBalance.getId(), record.getChangeBalance());

            if (updated != 1) {
                throw new ProviderException("处理冻结金额失败, recordId=" + record.getId());
            }

            // 更新变更记录
            if (isDecrease) {
                record.setType(MerchantStoreBalanceChangeRecordTypeEnum.CONSUMPTION.getType());
                merchantStoreBalanceChangeRecordRepository.updateById(record);
            } else {
                // 删除原先的冻结记录
                merchantStoreBalanceChangeRecordRepository.removeById(record.getId());
            }

            log.info("冻结金额{}成功, 订单: {}, 账户:{}, 变更记录: {}",
                    isDecrease ? "扣减" : "释放",
                    record.getAssociatedOrderNo(), record.getFundAccountId(), record);
        }
    }

    private void processPrepaymentBalance(Long tenantId,
                                          List<PaymentCombinedOrderDetail> orderDetails,
                                          List<String> orderNos, boolean isDecrease) {
        List<Long> orderIds = orderDetails.stream()
                .map(PaymentCombinedOrderDetail::getOrderId)
                .collect(Collectors.toList());
        List<TenantPrepaymentTransactionItem> prepayItems = tenantPrepaymentTransactionItemService.queryByOrderIds(tenantId, orderIds);

        if (CollectionUtils.isEmpty(prepayItems)) {
            return;
        }

        // 处理预付账户余额
        Map<Long, BigDecimal> prepayAccountAmountMap = prepayItems.stream()
                .collect(Collectors.groupingBy(
                        TenantPrepaymentTransactionItem::getPrepaymentAccountId,
                        Collectors.mapping(TenantPrepaymentTransactionItem::getTransactionAmount,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))
                ));

        for (Map.Entry<Long, BigDecimal> entry : prepayAccountAmountMap.entrySet()) {
            Long accountId = entry.getKey();
            BigDecimal amount = entry.getValue();

            int updated = isDecrease
                    ? tenantPrepaymentAccountService.decreaseFreezeBalance(accountId, amount)
                    : tenantPrepaymentAccountService.freezeAvailableAmount(accountId, amount.negate());

            if (updated != 1) {
                throw new ProviderException("处理预付冻结金额失败, accountId=" + accountId);
            }

            log.info("预付冻结金额{}成功, accountId: {}, 金额: {}, 关联订单: {}",
                    isDecrease ? "扣减" : "释放",
                    accountId, amount, orderNos);
        }

        // 如果是扣减操作，生成交易流水
        if (isDecrease) {
            generatePrepaymentTransactions(tenantId, prepayItems, orderNos);
        }

        // 更新预付交易明细
        prepayItems.forEach(item -> item.setDecreaseType(isDecrease
                ? DecreaseTypeEnum.DECREASE.getType()
                : DecreaseTypeEnum.RELEASE.getType()));
        tenantPrepaymentTransactionItemService.batchUpdateTransactionItem(prepayItems);
    }

    private void generatePrepaymentTransactions(Long tenantId,
                                                List<TenantPrepaymentTransactionItem> prepayItems,
                                                List<String> orderNos) {
        List<OrderResp> orders = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNos));
        if (CollectionUtils.isEmpty(orders)) {
            throw new ProviderException("根据订单号未查询到订单, orderNos=" + orderNos);
        }

        Map<Long, OrderResp> orderMap = orders.stream()
                .collect(Collectors.toMap(OrderResp::getId, o -> o));

        Map<Pair<Long, Integer>, BigDecimal> orderTypeAmountMap = prepayItems.stream()
                .collect(Collectors.groupingBy(
                        item -> Pair.of(item.getOrderId(), item.getTransactionType()),
                        Collectors.mapping(TenantPrepaymentTransactionItem::getTransactionAmount,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))
                ));

        List<TenantPrepaymentTransaction> transactions = new ArrayList<>();
        String transactionNo = IdUtil.getSnowflakeNextIdStr();
        for (Map.Entry<Pair<Long, Integer>, BigDecimal> entry : orderTypeAmountMap.entrySet()) {
            Pair<Long, Integer> key = entry.getKey();
            OrderResp order = orderMap.get(key.getKey());
            if (order == null) {
                throw new ProviderException("交易生成失败，未找到订单信息, orderId=" + key.getKey());
            }

            TenantPrepaymentTransaction transaction = new TenantPrepaymentTransaction();
            transaction.setTenantId(tenantId);
            transaction.setSupplierTenantId(order.getSupplierTenantId());
            transaction.setTransactionNo(transactionNo);
            transaction.setType(TenantPrepayTransactionEnum.Type.EXPENDITURE.getType());
            transaction.setTransactionType(key.getValue());
            transaction.setTransactionAmount(entry.getValue());
            transaction.setAssociatedOrderNo(order.getOrderNo());

            transactions.add(transaction);
        }

        if (!transactions.isEmpty()) {
            tenantPrepaymentTransactionService.batchInsertTransaction(transactions);
            log.info("生成预付交易流水记录, transactionNo: {}, size: {}", transactionNo, transactions.size());
        }
    }

    private void updateCombinedDetailStatus(Long combinedDetailId, PaymentEnum.Status targetStatus) {
        int updated = updateStatus(
                combinedDetailId,
                targetStatus.getCode(),
                PaymentEnum.Status.FREEZE.getCode()
        );
        if (updated != 1) {
            throw new ProviderException("更新组合支付子单状态失败, combinedDetailId=" + combinedDetailId);
        }
    }

    private boolean checkCombinedDetail(PaymentCombinedDetail detail) {
        if (detail == null) {
            log.error("组合支付明细为空，请检查参数");
            return false;
        }
        if (!Objects.equals(detail.getTradeType(), TradeTypeEnum.BALANCE.getDesc()) &&
                !Objects.equals(detail.getTradeType(), TradeTypeEnum.NON_CASH_PAY.getDesc())) {
            log.error("组合支付明细类型非余额或者非现金支付，结束处理");
            return false;
        }
        if (!Objects.equals(detail.getStatus(), PaymentEnum.Status.FREEZE.getCode())) {
            log.error("组合支付明细状态非冻结，结束处理");
            return false;
        }
        return true;
    }

    /**
     * 根据订单查询组合支付明细
     * 例如：orderA、orderB，有一笔成功的支付单【组合支付】，那么获取到组合明细【余额 + 微信】
     *
     * @param orderId
     * @return
     */
    @Override
    public List<PaymentCombinedDetail> querySuccessCombinedByOrderId(Long tenantId, Long orderId) {
        Payment payment = paymentMapper.querySuccessByOrderId(tenantId, orderId);
        if (payment == null) {
            return Collections.emptyList();
        }
        if (!Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            return Collections.emptyList();
        }
        return selectByCombinedPaymentNo(payment.getPaymentNo());
    }

    @Override
    public PaymentCombinedDetail querySuccessNativeCombinedDetailByOrderId(Long tenantId, Long orderId) {
        List<PaymentCombinedDetail> details = querySuccessCombinedByOrderId(tenantId, orderId);
        if (CollectionUtils.isEmpty(details)) {
            return null;
        }
        return details.stream()
                .filter(el -> Objects.isNull(el.getOnlinePayChannel()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public List<PaymentCombinedDetail> selectByCombinedPaymentNo(String combinePaymentNo) {
        return paymentCombinedDetailMapper.selectByCombinedPaymentNo(combinePaymentNo);
    }

    @Override
    public int updateByPrimaryKeySelective(PaymentCombinedDetail detail) {
        return paymentCombinedDetailMapper.updateByPrimaryKeySelective(detail);
    }

    @Override
    public int

    updateStatus(Long id, Integer currentStatus, Integer beforeStatus) {
        return paymentCombinedDetailMapper.updateStatus(id, currentStatus, beforeStatus);
    }
}
