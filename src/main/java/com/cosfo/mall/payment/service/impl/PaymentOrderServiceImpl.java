package com.cosfo.mall.payment.service.impl;

import com.cosfo.mall.payment.service.PaymentOrderService;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.event.OrderPaySuccessReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2024-08-22
 **/
@Service
@Slf4j
public class PaymentOrderServiceImpl implements PaymentOrderService {

    @Resource
    private OrderQueryProvider orderQueryProvider;
    @Resource
    private OrderCommandProvider orderCommandProvider;

    @Override
    public void orderPaySuccess(List<Long> orderIds) {
        // 批量查询订单状态
        List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByIds(orderIds));
        Map<Long, OrderResp> orderMap = orderList.stream().collect(Collectors.toMap(OrderResp::getId, Function.identity(), (v1, v2) -> v1));
        for (Long orderId : orderIds) {
            OrderResp order = orderMap.get(orderId);
            // 判断订单是否支付成功
            OrderPaySuccessReq orderPaySuccessReq = new OrderPaySuccessReq();
            orderPaySuccessReq.setOrderId(orderId);
            DubboResponse<Boolean> updateStatusResp = orderCommandProvider.paySuccess(orderPaySuccessReq);
            Boolean updateStatusFlag = updateStatusResp.getData();
            if (updateStatusFlag != null && updateStatusFlag) {
                log.info("更新订单:{}状态成功", order.getOrderNo());
            } else {
                processNeedRefund(order);
            }
        }
    }

    /**
     * 判断是否需要进行退款
     *
     * @param orderDTO
     */
    private void processNeedRefund(OrderResp orderDTO) {
        orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderDTO.getId()));
        if (Objects.equals(orderDTO.getStatus(), OrderStatusEnum.CANCELED.getCode())) {
            String message = String.format("订单：[%s]回调流程处理，订单已被取消，请手动生成退款单", orderDTO.getOrderNo());
            log.error(message, new BizException(message));
            return;
        }
        throw new ProviderException("更新订单状态失败");
    }

    @Override
    public void orderCompensation(List<Long> orderIds) {
        List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByIds(orderIds));
        orderList.stream().filter(el -> Objects.equals(el.getStatus(), OrderStatusEnum.NO_PAYMENT.getCode())).forEach(el -> {
            log.info("订单{}状态为待支付，调用订单中心进行补偿", el.getOrderNo());
            orderPaySuccess(Collections.singletonList(el.getId()));
            log.info("订单{}状态补偿成功", el.getOrderNo());
        });
    }
}
