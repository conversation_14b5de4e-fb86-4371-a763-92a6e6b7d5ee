package com.cosfo.mall.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.payment.model.po.HuiFuPaymentRateRetry;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-04
 * @Description:
 */
public interface HuiFuPaymentRateRetryService extends IService<HuiFuPaymentRateRetry> {
    /**
     * 根据支付单id删除
     * @param id
     */
    void deleteByPaymentId(Long id);

    /**
     * 增大重试次数
     * @param id
     */
    void increaseRetryNumById(Long id);

    /**
     * 根据时间范围查询需要重试的支付单
     * @param startTime
     * @param endTime
     * @return 单次最大50条
     */
    List<HuiFuPaymentRateRetry> selectListByTime(LocalDateTime startTime,LocalDateTime endTime);
}
