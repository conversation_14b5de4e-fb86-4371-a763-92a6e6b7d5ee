package com.cosfo.mall.payment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.payment.mapper.HuiFuPaymentRateRetryMapper;
import com.cosfo.mall.payment.model.po.HuiFuPaymentRateRetry;
import com.cosfo.mall.payment.service.HuiFuPaymentRateRetryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-04
 * @Description:
 */
@Service
public class HuiFuPaymentRateRetryServiceImpl extends ServiceImpl<HuiFuPaymentRateRetryMapper, HuiFuPaymentRateRetry> implements HuiFuPaymentRateRetryService {

    @Resource
    private HuiFuPaymentRateRetryMapper huiFuPaymentRateRetryMapper;

    @Override
    public void deleteByPaymentId(Long paymentId) {
        LambdaQueryWrapper<HuiFuPaymentRateRetry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HuiFuPaymentRateRetry::getPaymentId, paymentId);
        remove(queryWrapper);
    }

    @Override
    public void increaseRetryNumById(Long id) {
        huiFuPaymentRateRetryMapper.increaseRetryNum(id);
    }

    @Override
    public List<HuiFuPaymentRateRetry> selectListByTime(LocalDateTime startTime, LocalDateTime endTime) {
        return huiFuPaymentRateRetryMapper.selectListByTime(startTime, endTime);
    }

}
