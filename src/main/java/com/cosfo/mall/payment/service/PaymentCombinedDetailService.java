package com.cosfo.mall.payment.service;

import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;

import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2025-04-27
 **/
public interface PaymentCombinedDetailService {

    /**
     * 实际金额扣减
     * 组合支付回调会触发 将冻结金额实际扣除
     *
     * @param detail
     */
    void decreaseFreezeAmount(PaymentCombinedDetail detail);

    /**
     * 冻结金额释放
     * 组合支付后，现结（微信、支付宝）没有实际支付
     * 1、用户取消订单 2、订单延迟关单消息 3、用户再次发起支付，关闭上次支付
     *
     * @param detail
     */
    void releaseFreezeAmount(PaymentCombinedDetail detail);

    /**
     * 根据订单查询组合支付明细
     *
     * @param orderId
     * @return
     */
    List<PaymentCombinedDetail> querySuccessCombinedByOrderId(Long tenantId, Long orderId);

    /**
     * 本地组合支付明细
     * 余额 + 微信 进行组合支付 返回余额明细
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    PaymentCombinedDetail querySuccessNativeCombinedDetailByOrderId(Long tenantId, Long orderId);

    /**
     * 根据组合支付单号查询支付明细
     *
     * @param combinePaymentNo
     * @return
     */
    List<PaymentCombinedDetail> selectByCombinedPaymentNo(String combinePaymentNo);

    /**
     * 更新组合支付明细状态
     *
     * @param id
     * @param currentStatus 当前状态
     * @param beforeStatus  修改状态
     * @return
     */
    int updateStatus(Long id, Integer currentStatus, Integer beforeStatus);

    /**
     * 更新
     *
     * @param detail
     * @return
     */
    int updateByPrimaryKeySelective(PaymentCombinedDetail detail);

}
