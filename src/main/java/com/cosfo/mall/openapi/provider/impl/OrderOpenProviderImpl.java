package com.cosfo.mall.openapi.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.mall.client.openapi.provider.OrderOpenProvider;
import com.cosfo.mall.client.openapi.req.CancelOrderReq;
import com.cosfo.mall.client.openapi.req.PlaceOrderReq;
import com.cosfo.mall.client.openapi.req.PlaceOrderReq.PlaceOrderItem;
import com.cosfo.mall.client.openapi.resp.PlaceOrderResp;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import com.cosfo.mall.common.constants.PlaceOrderItemKeyEnum;
import com.cosfo.mall.common.constants.RedisKeyEnum;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.exception.OpenApiProviderException;
import com.cosfo.mall.common.exception.code.OpenApiErrorCode;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.common.utils.ThreadTokenHolder;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.facade.WarehouseStorageFenceQueryFacade;
import com.cosfo.mall.facade.WarehouseStorageQueryFacade;
import com.cosfo.mall.facade.ofc.OfcDeliveryInfoFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantContactFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.mall.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.mall.merchant.convert.MerchantAddressMapperConvert;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.openapi.convert.StockConvert;
import com.cosfo.mall.openapi.model.bo.OrderBO;
import com.cosfo.mall.openapi.model.bo.OrderItemBO;
import com.cosfo.mall.openapi.model.dto.ItemRouteDTO;
import com.cosfo.mall.openapi.model.dto.PlaceOrderItemDetailDTO;
import com.cosfo.mall.openapi.model.dto.item.ItemPriceDTO;
import com.cosfo.mall.openapi.model.dto.item.MarketItemDTO;
import com.cosfo.mall.openapi.service.FeishuNotifyService;
import com.cosfo.mall.openapi.service.MarketItemBizService;
import com.cosfo.mall.openapi.service.OrderBizService;
import com.cosfo.mall.order.converter.OrderAddressConverter;
import com.cosfo.mall.order.factory.OrderDeliveryDateStrategy;
import com.cosfo.mall.order.model.dto.OrderItemAfterSaleRuleDTO;
import com.cosfo.mall.order.model.vo.OrderAfterSaleRuleDetailVO;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.stock.model.dto.PreDistributionOrderItemDTO;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.cosfo.mall.stock.model.dto.StockQueryDTO;
import com.cosfo.mall.stock.service.StockService;
import com.cosfo.mall.tenant.service.SpecialTenantService;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleRuleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.req.event.OrderCloseReq;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleRuleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.summerfarm.wnc.client.req.WarehouseStorageFenceQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseStorageDetailResp;
import net.summerfarm.wnc.client.resp.WarehouseStorageFenceRuleResp;
import net.xianmu.common.account.IsvInfo;
import net.xianmu.common.account.IsvInfoHolder;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 开发平台-订单
 *
 * @author: xiaowk
 * @time: 2023/9/18 下午5:06
 */
@Slf4j
@DubboService
public class OrderOpenProviderImpl implements OrderOpenProvider {

    @Resource
    private MarketItemBizService marketItemBizService;
    @Resource
    private OrderBizService orderBizService;
    @Resource
    private StockService stockService;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;
    @Resource
    private UserCenterMerchantContactFacade userCenterMerchantContactFacade;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private PaymentService paymentService;
    @Resource
    private OfcDeliveryInfoFacade ofcDeliveryInfoFacade;
    @Resource
    private FeishuNotifyService feishuNotifyService;
    @Resource
    private WarehouseStorageFenceQueryFacade warehouseStorageFenceQueryFacade;
    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private OrderDeliveryDateStrategy orderDeliveryDateStrategy;
    @Resource
    private SpecialTenantService specialTenantService;

    @DubboReference
    private OrderAfterSaleRuleQueryProvider orderAfterSaleRuleQueryProvider;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderCommandProvider orderCommandProvider;

    /**
     * 下单支持商品类型
     */
    public static Set<Integer> OPEN_ORDER_SUPPORT_GOODS_TYPE_SET = Sets.newHashSet(GoodsTypeEnum.QUOTATION_TYPE.getCode(), GoodsTypeEnum.SELF_GOOD_TYPE.getCode());

    public static Set<Integer> CLOSE_ORDER_SUPPORT_ORDER_STATUS_SET = Sets.newHashSet(OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode());

    @Override
    public DubboResponse<PlaceOrderResp> placeOrder(@Valid PlaceOrderReq placeOrderReq) {
        PlaceOrderResp placeOrderResp = placeOrderForRoute(placeOrderReq, SKU_ROUTE_FUNCTION);
        return DubboResponse.getOK(placeOrderResp);
    }

    @Override
    public DubboResponse<Void> cancelOrder(@Valid CancelOrderReq cancelOrderReq) {
        Long tenantId = Optional.ofNullable(IsvInfoHolder.getAccount()).map(IsvInfo::getAccountId).orElse(null);
        if (tenantId == null) {
            throw new BizException("tenantId不能为空");
        }
        // 根据外部订单号查询订单信息
        String customerOrderId = cancelOrderReq.getCustomerOrderId();
        OrderQueryReq queryReq = new OrderQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setCustomerOrderIds(Collections.singletonList(customerOrderId));
        List<OrderResp> orderDTOS = RpcResultUtil.handle(orderQueryProvider.queryOrderList(queryReq));
        orderDTOS = orderDTOS.stream().filter(e -> !OrderStatusEnum.CANCELED.getCode().equals(e.getStatus())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(orderDTOS)) {
            throw new BizException("不存在正常状态的订单记录，customerOrderId=" + customerOrderId, OpenApiErrorCode.OR_NOT_EXIST_CUSTOMER);
        }
        OrderResp orderDTO = orderDTOS.get(NumberConstant.ZERO);

        if (!CLOSE_ORDER_SUPPORT_ORDER_STATUS_SET.contains(orderDTO.getStatus())) {
            throw new BizException("订单非待配送状态不可取消，customerOrderId=" + customerOrderId, OpenApiErrorCode.OR_CLOSE_NOT_SUPPORT_STATUS);
        }
        OrderCloseReq orderCloseReq = new OrderCloseReq();
        orderCloseReq.setTenantId(tenantId);
        orderCloseReq.setOrderId(orderDTO.getId());
        DubboResponse<Boolean> response = orderCommandProvider.close(orderCloseReq);
        if (!response.isSuccess() || !response.getData()) {
            throw new BizException("关闭订单失败");
        }
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<PlaceOrderResp> placeOrderByItemCode(@Valid PlaceOrderReq placeOrderReq) {
        PlaceOrderResp placeOrderResp = placeOrderForRoute(placeOrderReq, ITEM_CODE_ROUTE_FUNCTION);
        return DubboResponse.getOK(placeOrderResp);
    }

    /**
     * sku路由规则
     */
    private Function<ItemRouteDTO, PlaceOrderItemDetailDTO> SKU_ROUTE_FUNCTION = new Function<ItemRouteDTO, PlaceOrderItemDetailDTO>() {
        @Override
        public PlaceOrderItemDetailDTO apply(ItemRouteDTO itemRouteDTO) {
            Map<String, MarketItemDTO> marketItemDTOMap = marketItemBizService.queryXianmuWarehouseItemBySkuCodes(itemRouteDTO.getTenantId(), itemRouteDTO.getCodeList());
            return PlaceOrderItemDetailDTO.builder().marketItemDTOMap(marketItemDTOMap).
                    itemKeyEnum(PlaceOrderItemKeyEnum.SKU).build();
        }
    };

    /**
     * 自有编码路由规则
     */
    private Function<ItemRouteDTO, PlaceOrderItemDetailDTO> ITEM_CODE_ROUTE_FUNCTION = new Function<ItemRouteDTO, PlaceOrderItemDetailDTO>() {
        @Override
        public PlaceOrderItemDetailDTO apply(ItemRouteDTO itemRouteDTO) {
            Map<String, MarketItemDTO> marketItemDTOMap = marketItemBizService.queryXianmuWarehouseItemByItemCodes(itemRouteDTO);
            return PlaceOrderItemDetailDTO.builder().marketItemDTOMap(marketItemDTOMap).
                    itemKeyEnum(PlaceOrderItemKeyEnum.ITEM_CODE).build();
        }
    };

    /**
     * 根据不同商品路由函数进行下单
     * @param placeOrderReq
     * @param ITEM_ROUTE_FUNCTION
     * @return
     */
    private PlaceOrderResp placeOrderForRoute(PlaceOrderReq placeOrderReq, Function<ItemRouteDTO, PlaceOrderItemDetailDTO> ITEM_ROUTE_FUNCTION) {
        Long tenantId = Optional.ofNullable(IsvInfoHolder.getAccount()).map(IsvInfo::getAccountId).orElseThrow(() -> new BizException("tenantId不能为空"));

        // 下单参数
        OrderBO orderBO = new OrderBO();
        orderBO.setTenantId(tenantId);
        // 开放平台接口下单， 订单来源：0：内部系统;1：openapi调用
        orderBO.setOrderSource(1);

        String storeNo = placeOrderReq.getStoreNo();
        String customerOrderId = placeOrderReq.getCustomerOrderId();
        orderBO.setCustomerOrderId(customerOrderId);

        // 霸王茶姬对接项目新增字段
        specialTenantService.validatePlaceOrderParam(tenantId, placeOrderReq);
        orderBO.setCustomerOrderExtraNo(placeOrderReq.getCustomerOrderExtraNo());
        orderBO.setExpectedDeliveryDate(placeOrderReq.getExpectedDeliveryDate());
        orderBO.setOrderPaymentTime(placeOrderReq.getOrderPaymentTime());
        orderBO.setOverTimeOrder(placeOrderReq.getOverTimeOrder());
        orderBO.setOrderPriority(placeOrderReq.getOrderPriority());

        List<String> keys = Lists.newArrayList();
        try {
            // customerOrderId加锁
            lockOrder(keys, Collections.singletonList(customerOrderId));

            // 判断order表中是否有重复下单记录
            OrderQueryReq queryReq = new OrderQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setCustomerOrderIds(Collections.singletonList(customerOrderId));
            List<OrderResp> orderDTOS = RpcResultUtil.handle(orderQueryProvider.queryOrderList(queryReq));
            if (!CollectionUtils.isEmpty(orderDTOS)) {
                orderDTOS = orderDTOS.stream().filter(e -> !OrderStatusEnum.CANCELED.getCode().equals(e.getStatus())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(orderDTOS)) {
                    throw new BizException("存在重复订单记录，customerOrderId=" + customerOrderId, OpenApiErrorCode.OR_EXIST_REPEAT_ORDER);
                }
            }

            List<PlaceOrderItem> orderItemList = placeOrderReq.getOrderItemList();
            List<String> skuCodeList = orderItemList.stream().map(PlaceOrderItem::getSkuCode).distinct().collect(Collectors.toList());
            if (skuCodeList.size() != orderItemList.size()) {
                throw new BizException("下单商品有重复项skuCode", OpenApiErrorCode.OR_EXIST_REPEAT_SKUCODE);
            }

            List<String> repeatCustomerOrderItemIds = orderItemList.stream()
                    .map(PlaceOrderItem::getCustomerOrderItemId)
                    .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                    .entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Entry::getKey)
                    .collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(repeatCustomerOrderItemIds)){
                throw new BizException("下单商品有重复项customerOrderItemId", OpenApiErrorCode.OR_EXIST_REPEAT_ORDER_ITEM_ID);
            }

            Map<String, PlaceOrderItem> sku2PlaceOrderItemMap = orderItemList.stream().collect(Collectors.toMap(PlaceOrderItem::getSkuCode, Function.identity(), (v1, v2) -> v1));

            MerchantStoreResultResp storeResultResp = userCenterMerchantStoreFacade.getMerchantStoreByStoreNo(tenantId, storeNo);
            if (storeResultResp == null || storeResultResp.getId() == null) {
                throw new BizException("门店信息不存在, storeNo=" + storeNo);
            }

            Long storeId = storeResultResp.getId();
            MerchantAddress merchantAddress = merchantAddressService.selectByStoreId(storeId, tenantId);
            if (Objects.isNull(merchantAddress)) {
                throw new BizException("门店地址不存在, storeNo=" + storeNo);
            }
            if (!StringUtils.isEmpty(merchantAddress.getHouseNumber())) {
                merchantAddress.setAddress(merchantAddress.getAddress().contains(merchantAddress.getHouseNumber()) ? merchantAddress.getAddress() : merchantAddress.getAddress() + merchantAddress.getHouseNumber());
            }

            MerchantStoreAccountResultResp merchantStoreAccountResultResp = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountByStoreId(tenantId, storeId);
            if (merchantStoreAccountResultResp == null) {
                throw new BizException("门店账号不存在, storeNo=" + storeNo);
            }
            Long accountId = merchantStoreAccountResultResp.getId();

            orderBO.setStoreId(storeId);
            orderBO.setStoreName(storeResultResp.getStoreName());
            orderBO.setAccountId(accountId);

            OrderAddressDTO orderAddressDTO = OrderAddressConverter.INSTANCE.merchantAddress2OrderAddress(merchantAddress);

            MerchantContactResultResp merchantContactResultResp = userCenterMerchantContactFacade.getStoreContactByStoreId(tenantId, storeId);
            if (merchantContactResultResp == null) {
                throw new BizException("门店联系人不存在, storeNo=" + storeNo);
            }
            orderAddressDTO.setContactPhone(merchantContactResultResp.getPhone());
            orderAddressDTO.setContactName(merchantContactResultResp.getName());

            // 下单接口指定联系人和手机号
            if (!StringUtils.isBlank(placeOrderReq.getOrderContactName())){
                orderAddressDTO.setContactName(placeOrderReq.getOrderContactName());
            }
            if (!StringUtils.isBlank(placeOrderReq.getOrderContactPhone())){
                orderAddressDTO.setContactPhone(placeOrderReq.getOrderContactPhone());
            }
            orderBO.setOrderAddressDTO(orderAddressDTO);

            MerchantAddressDTO merchantAddressDTO = MerchantAddressMapperConvert.INSTANCE.address2Dto(merchantAddress);

            try {
                LocalDateTime orderPayTime = DateUtil.parseLocalDateTime(placeOrderReq.getOrderPaymentTime());
                DeliveryDateQueryResp resp = ofcDeliveryInfoFacade.queryDeliveryDate(merchantAddressDTO, orderPayTime, placeOrderReq.getOverTimeOrder());

                LocalDateTime deliveryDateTime = orderDeliveryDateStrategy.calculateOrderDeliveryDate(tenantId, placeOrderReq, resp);
                orderBO.setDeliveryTime(deliveryDateTime);
                orderBO.setFulfillmentType(resp.getFulfillmentType());

            } catch (BizException e) {
                if (OpenApiErrorCode.OR_DELIVERY_DATE_ERROR.equals(e.getErrorCode())){
                    log.error("{}期望配送日期{}不支持配送", JSON.toJSONString(merchantAddressDTO), placeOrderReq.getExpectedDeliveryDate(), e);
                    throw new BizException(e.getMessage(), OpenApiErrorCode.OR_DELIVERY_DATE_ERROR);
                }
                log.error("{}获取配送时间错误", JSON.toJSONString(merchantAddressDTO), e);
                throw new BizException("门店地址不支持配送", OpenApiErrorCode.OR_ADDRESS_NOT_SUPPORTED_DELIVERY);
            } catch (Exception e) {
                log.error("{}获取配送时间错误", JSON.toJSONString(merchantAddressDTO), e);
                throw new BizException("服务调用异常，请重试");
            }

            // 根据路由规则匹配出商品信息
            ItemRouteDTO itemRouteDTO = ItemRouteDTO.builder().tenantId(tenantId).storeId(storeId).codeList(skuCodeList)
                    .merchantAddressDTO(merchantAddressDTO).build();
            PlaceOrderItemDetailDTO placeOrderItemDetailDTO = ITEM_ROUTE_FUNCTION.apply(itemRouteDTO);
            log.info("路由后商品信息,placeOrderItemDetailDTO:{}",JSON.toJSONString(placeOrderItemDetailDTO));

            List<MarketItemDTO> notXianmuWarehouseItemList = placeOrderItemDetailDTO.getMarketItemDTOMap().values().stream().
                    filter(e -> !OPEN_ORDER_SUPPORT_GOODS_TYPE_SET.contains(e.getGoodsType())).collect(Collectors.toList());
            // 有非直供、代仓商品
            if (!CollectionUtils.isEmpty(notXianmuWarehouseItemList)) {
                String errSkuCodes = notXianmuWarehouseItemList.stream().map(MarketItemDTO::getAgentSkuCode).collect(Collectors.joining(","));
                throw new BizException(errSkuCodes + "不是鲜沐直供/自营代仓商品，不支持下单", OpenApiErrorCode.OR_EXIST_NOT_QUOTATION_ITEM);
            }

            // 下单商品数量
            Map<String, Integer> itemQuantityMap = orderItemList.stream().collect(Collectors.toMap(PlaceOrderItem::getSkuCode, PlaceOrderItem::getQuantity, (v1, v2) -> v1));

            // 特殊租户下单不需要占用库存
            boolean needOccupyInventory = !specialTenantService.placeOrderNotNeedOccupyInventory(tenantId);

            // 下单查询库存
            StockQueryDTO stockQueryDTO = new StockQueryDTO();
            stockQueryDTO.setTenantId(tenantId);
            stockQueryDTO.setMerchantAddressDTO(merchantAddressDTO);
            stockQueryDTO.setPreDistributionOrderItemDTOList(StockConvert.convertPreDisDTO(placeOrderItemDetailDTO, itemQuantityMap));
            if (needOccupyInventory){
                Map<Long, StockDTO> stockDTOMap = stockService.preDistributionOrderOccupy(stockQueryDTO, true);

                checkStock(stockDTOMap, placeOrderItemDetailDTO, merchantAddressDTO, itemQuantityMap, customerOrderId, stockQueryDTO);
            }

            // 商品价格
            Map<Long, ItemPriceDTO> itemPriceDTOMap = marketItemBizService.queryItemPrice(tenantId, storeId, placeOrderItemDetailDTO);

            // 组装订单对象上下文参数
            buildThreePartiesOrderBO(orderBO, placeOrderItemDetailDTO, itemQuantityMap, itemPriceDTOMap, sku2PlaceOrderItemMap);

            // 初始化保存订单记录，状态【1-下单中】
            orderBizService.createOrder(orderBO);

            // 锁定库存，如果成功，更新订单【2-待支付】
            boolean lockStockFlag = orderBizService.placeOrderLockStock(orderBO, true, needOccupyInventory);

            // 锁定库存失败，查询具体商品库存不足信息返回
            if (!lockStockFlag && needOccupyInventory) {
                Map<Long, StockDTO> newStockDTOMap = stockService.preDistributionOrderOccupy(stockQueryDTO, true);
                checkStock(newStockDTOMap, placeOrderItemDetailDTO, merchantAddressDTO, itemQuantityMap, customerOrderId, stockQueryDTO);
                throw new BizException("商品库存不足", OpenApiErrorCode.OR_ITEM_OUT_OF_STOCK);
            }

            boolean paySuccessFlag = false;
            try {
                ThreadTokenHolder.clearMerchantInfo();

                // 调用 账期支付接口，更新订单为【10-待出库】
                PaymentRequest paymentRequest = new PaymentRequest();
                paymentRequest.setOrderNos(Collections.singletonList(orderBO.getOrderNo()));
                paymentRequest.setPayType(PayTypeEnum.BILL.getCode());
                paymentRequest.setH5Request(false);
                PaymentResult result = paymentService.pay(paymentRequest);

                paySuccessFlag = result.isSuccess();
            } catch (Exception e) {
                log.error("开放平台下单账期支付失败，orderNO={}", orderBO.getOrderNo(), e);
                paySuccessFlag = false;
            }

            // 支付失败，取消订单
            if (!paySuccessFlag) {
                orderBizService.cancelOrderAfterLockStockFail(orderBO);
                throw new OpenApiProviderException("下单失败，记录支付账单异常，customerOrderId=" + customerOrderId, OpenApiErrorCode.OR_PAY_ORDER_ERROR);
            } else {
                // 记录成功的订单
                logSuccessOrders(customerOrderId, placeOrderReq, placeOrderItemDetailDTO, itemQuantityMap, stockQueryDTO);
            }

        } finally {
            unlockOrder(keys);
        }

        PlaceOrderResp placeOrderResp = new PlaceOrderResp();
        placeOrderResp.setCustomerOrderId(customerOrderId);
        return placeOrderResp;
    }

    private void checkStock(Map<Long, StockDTO> stockDTOMap, PlaceOrderItemDetailDTO placeOrderItemDetailDTO, MerchantAddressDTO merchantAddressDTO, Map<String, Integer> itemQuantityMap, String customerOrderId,
                            StockQueryDTO stockQueryDTO) {
        // 是否库存不足
        boolean outofstockFlag = false;

        Long tenantId = null;
        StringBuilder outsideErrorMsg = new StringBuilder();
        StringBuilder insideErrorMsg = new StringBuilder();
        outsideErrorMsg.append("商品库存不足");
        insideErrorMsg.append("商品库存不足");

        Set<String> outStockSkuSet = Sets.newHashSet();
        for (Entry<String, MarketItemDTO> entry : placeOrderItemDetailDTO.getMarketItemDTOMap().entrySet()) {
            MarketItemDTO marketItemDTO = entry.getValue();
            String apiCode = entry.getKey();
            tenantId = marketItemDTO.getTenantId();
            StockDTO stockDTO = stockDTOMap.get(marketItemDTO.getItemId());
            if (stockDTO == null || stockDTO.getAmount() == null || stockDTO.getAmount() <= 0 || stockDTO.getAmount().compareTo(itemQuantityMap.get(apiCode)) < 0) {
                outsideErrorMsg.append(",").append(apiCode).append(":").append(marketItemDTO.getTitle()).append(",下单数量:").append(itemQuantityMap.get(apiCode));
                // 得到确切的skuCode
                String agentSkuCode = marketItemDTO.getAgentSkuCode();
                insideErrorMsg.append(",").append(agentSkuCode).append(":").append(marketItemDTO.getTitle()).append(",下单数量:").append(itemQuantityMap.get(apiCode));
                outStockSkuSet.add(agentSkuCode);
                outofstockFlag = true;
            }
        }

        if (outofstockFlag) {
            TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);
            String tenantName = Optional.ofNullable(tenant).map(TenantResultResp::getTenantName).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
            String warehouseNames = queryNotifyWarehouseInfo(stockQueryDTO, outStockSkuSet, true);
            StringBuilder addressSb = new StringBuilder();
            addressSb.append(merchantAddressDTO.getProvince()).append(merchantAddressDTO.getCity())
                    .append(merchantAddressDTO.getArea()).append(merchantAddressDTO.getAddress());
            feishuNotifyService.placeOrderOutofStockNotify(tenantId, merchantAddressDTO.getStoreId(), addressSb.toString(), insideErrorMsg.toString(), customerOrderId, tenantName, warehouseNames);
            throw new BizException(outsideErrorMsg.toString(), OpenApiErrorCode.OR_ITEM_OUT_OF_STOCK);
        }
    }

    @Async
    public void logSuccessOrders(String customerOrderId, PlaceOrderReq placeOrderReq, PlaceOrderItemDetailDTO placeOrderItemDetailDTO, Map<String, Integer> itemQuantityMap,
        StockQueryDTO stockQueryDTO) {
        try{
            MerchantAddressDTO addressDTO = stockQueryDTO.getMerchantAddressDTO();
            Set<String> skuSet = placeOrderItemDetailDTO.getMarketItemDTOMap().values().stream().map(marketItemDTO -> marketItemDTO.getAgentSkuCode()).collect(Collectors.toSet());
            String warehouseNames = queryNotifyWarehouseInfo(stockQueryDTO, skuSet, true);
            for (Entry<String, MarketItemDTO> entry : placeOrderItemDetailDTO.getMarketItemDTOMap().entrySet()) {
                String apiCode = entry.getKey();
                MarketItemDTO marketItemDTO = entry.getValue();

                Map<String,Object> logging = new HashMap<>();
                logging.put("customerOrderId",customerOrderId);
                logging.put("sku", String.format("%s-%s", marketItemDTO.getAgentSkuCode(), marketItemDTO.getTitle()));
                logging.put("tenantId", marketItemDTO.getTenantId());
                logging.put("skuSpec", marketItemDTO.getSpecification());
                logging.put("quantity", itemQuantityMap.get(apiCode));
                logging.put("warehouse", warehouseNames);
                log.info("开放平台下单成功!:{},PlaceOrderReq:{}, storeInfo:{}", JSON.toJSONString(logging), JSON.toJSONString(placeOrderReq), JSON.toJSONString(addressDTO));
            }
        }catch (Exception e){
            log.warn("记录开放平台下单成功异常,customerOrderId:{}, placeOrderItemDetailDTO:{}", customerOrderId, placeOrderItemDetailDTO, e);
        }
    }

    /**
     * 查询满足配送规则的仓库，进行通知
     * @param stockQueryDTO
     * @return
     */
    private String queryNotifyWarehouseInfo(StockQueryDTO stockQueryDTO, Set<String> skuSet, boolean openApiReq) {
        if (CollectionUtils.isEmpty(stockQueryDTO.getPreDistributionOrderItemDTOList()) || CollectionUtils.isEmpty(skuSet)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }

        List<PreDistributionOrderItemDTO> preDistributionOrderItemDTOList = stockQueryDTO.getPreDistributionOrderItemDTOList();
        // 缺货的sku
        List<String> skuCodeList = preDistributionOrderItemDTOList.stream().filter(item -> !GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(item.getGoodsType())
                && skuSet.contains(item.getAgentSku()))
                .map(PreDistributionOrderItemDTO::getAgentSku).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(skuCodeList)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }

        WarehouseStorageFenceQueryReq warehouseStorageFenceQueryReq = new WarehouseStorageFenceQueryReq();
        MerchantAddressDTO merchantAddressDTO = stockQueryDTO.getMerchantAddressDTO();
        warehouseStorageFenceQueryReq.setTenantId(stockQueryDTO.getTenantId());
        warehouseStorageFenceQueryReq.setArea(merchantAddressDTO.getArea());
        warehouseStorageFenceQueryReq.setCity(merchantAddressDTO.getCity());
        warehouseStorageFenceQueryReq.setPoi(merchantAddressDTO.getPoiNote());
        warehouseStorageFenceQueryReq.setSkuList(skuCodeList);
        if (openApiReq) {
            warehouseStorageFenceQueryReq.setContactId(stockQueryDTO.getMerchantAddressDTO().getStoreId());
        }
        List<WarehouseStorageFenceRuleResp> warehouseStorageFenceRuleRespList = warehouseStorageFenceQueryFacade.queryWarehouseStorageFence(warehouseStorageFenceQueryReq);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(warehouseStorageFenceRuleRespList)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }

        // 过滤出鲜沐仓
        List<Integer> warehouseNoList = warehouseStorageFenceRuleRespList.stream().filter(resp -> XianmuSupplyTenant.TENANT_ID.equals(resp.getTenantId()))
                .map(WarehouseStorageFenceRuleResp::getWarehouseNo).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(warehouseNoList)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }

        List<WarehouseStorageDetailResp> respList = warehouseStorageQueryFacade.warehouseList(warehouseNoList);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(respList)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        String warehouseNames = respList.stream()
                .map(WarehouseStorageDetailResp::getWarehouseName)
                .collect(Collectors.joining(","));
        return warehouseNames;
    }


    private void buildThreePartiesOrderBO(OrderBO orderBO,
                                          PlaceOrderItemDetailDTO placeOrderItemDetailDTO,
                                          Map<String, Integer> itemQuantityMap,
                                          Map<Long, ItemPriceDTO> itemPriceDTOMap,
                                          Map<String, PlaceOrderItem> sku2PlaceOrderItemMap) {

        orderBO.setWarehouseType(WarehouseTypeEnum.THREE_PARTIES.getCode());
        orderBO.setPayType(PayTypeEnum.BILL.getCode());
        orderBO.setOrderType(OrderTypeEnum.ORDINARY_ORDER.getValue());
        Long supplierTenantId = WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderBO.getWarehouseType()) ? XianmuSupplyTenant.TENANT_ID : orderBO.getTenantId();
        orderBO.setSupplierTenantId(supplierTenantId);
        orderBO.setStatus(OrderStatusEnum.CREATING_ORDER.getCode());
        orderBO.setOrderVersion(1);

        BigDecimal payablePrice = BigDecimal.ZERO;
        List<OrderItemBO> orderItemBOS = new ArrayList<>(itemQuantityMap.size());

        for (Entry<String, MarketItemDTO> entry : placeOrderItemDetailDTO.getMarketItemDTOMap().entrySet()) {
            String apiCode = entry.getKey();
            MarketItemDTO marketItemDTO = entry.getValue();

            OrderItemBO orderItemBO = new OrderItemBO();
            orderItemBO.setItemId(marketItemDTO.getItemId());
            orderItemBO.setSkuId(marketItemDTO.getSkuId());
            orderItemBO.setSupplierSkuId(marketItemDTO.getAgentSkuId());
            orderItemBO.setAmount(itemQuantityMap.get(apiCode));

            ItemPriceDTO itemPriceDTO = itemPriceDTOMap.get(marketItemDTO.getItemId());
            orderItemBO.setPrice(itemPriceDTO.getPrice());
            // 供应价
            orderItemBO.setSupplyPrice(itemPriceDTO.getSupplyPrice());

            orderItemBO.setSupplierTenantId(marketItemDTO.getAgentTenantId());
            orderItemBO.setTitle(marketItemDTO.getTitle());
            orderItemBO.setMainPicture(marketItemDTO.getMainPicture());
            orderItemBO.setSpecification(marketItemDTO.getSpecification());
            orderItemBO.setSpecificationUnit(marketItemDTO.getSpecificationUnit());
            orderItemBO.setAfterSaleUnit(marketItemDTO.getAfterSaleUnit());
            orderItemBO.setMaxAfterSaleAmount(marketItemDTO.getMaxAfterSaleAmount());
            orderItemBO.setGoodsType(marketItemDTO.getGoodsType());
            orderItemBO.setSupplySku(marketItemDTO.getAgentSkuCode());
            orderItemBO.setItemCode(marketItemDTO.getItemCode());
            orderItemBO.setOrderType(orderBO.getOrderType());
            orderItemBO.setTenantId(orderBO.getTenantId());
            orderItemBO.setItemSaleMode(marketItemDTO.getItemSaleMode());
            orderItemBO.setSupplierName(marketItemDTO.getSupplierName());
            orderItemBO.setSkuCode(marketItemDTO.getAgentSkuCode());
            orderItemBO.setSkuTenantId(GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItemDTO.getGoodsType()) ? XianmuSupplyTenant.TENANT_ID : orderBO.getTenantId());

            PlaceOrderItem placeOrderItem = sku2PlaceOrderItemMap.get(apiCode);
            if (placeOrderItem != null) {
                orderItemBO.setCustomerOrderItemId(placeOrderItem.getCustomerOrderItemId());
                orderItemBO.setCustomerSkuCode(placeOrderItem.getCustomerSkuCode());
                orderItemBO.setCustomerSkuTitle(placeOrderItem.getCustomerSkuTitle());
                orderItemBO.setCustomerSkuSpecification(placeOrderItem.getCustomerSkuSpecification());
                orderItemBO.setCustomerSkuSpecificationUnit(placeOrderItem.getCustomerSkuSpecificationUnit());
            }

            // 售后规则计算
            OrderItemAfterSaleRuleDTO orderAfterSaleRule = getOrderAfterSaleRule(orderBO.getTenantId(), marketItemDTO.getClassificationId(), orderBO.getWarehouseType());
            // 计算最大可申请售后时间
            Integer applyEndTime = orderAfterSaleRule.getApplyEndTime();
            applyEndTime = Objects.nonNull(orderBO.getApplyEndTime())
                    ? orderBO.getApplyEndTime() < applyEndTime ? applyEndTime : orderBO.getApplyEndTime()
                    : applyEndTime;

            orderItemBO.setOrderAfterSaleRule(orderAfterSaleRule);
            orderBO.setApplyEndTime(applyEndTime);
            orderBO.setAutoFinishedTime(orderAfterSaleRule.getAutoFinishedTime());
            // 价格计算
            BigDecimal totalPrice = NumberUtil.mul(orderItemBO.getPrice(), orderItemBO.getAmount());
            payablePrice = payablePrice.add(totalPrice);
            orderItemBO.setTotalPrice(totalPrice);
            orderItemBOS.add(orderItemBO);
        }

        orderBO.setOrderItemBOS(orderItemBOS);
//        orderBO.setPayablePrice(payablePrice);
        // 查询运费
//        DeliveryFeeSnapshotVO deliveryFee = queryDeliveryFee(orderBO, loginContextInfoDTO, merchantAddressDTO, deliveryDate);
//        orderBO.setDeliveryFeeSnapshotVO(deliveryFee);
//        payablePrice = NumberUtil.add(payablePrice, deliveryFee.getDeliveryFee());
        orderBO.setDeliveryFee(BigDecimal.ZERO);
        orderBO.setPayablePrice(payablePrice);

    }


    /**
     * 获取申请售后规则
     *
     * @param tenantId         租户id
     * @param classificationId 商品分组Id
     * @param deliveryType     仓库类型 0无仓 1三方 2自营
     */
    private OrderItemAfterSaleRuleDTO getOrderAfterSaleRule(Long tenantId,
                                                            Long classificationId, Integer deliveryType) {

        // 查询售后规则
        List<OrderAfterSaleRuleResp> orderAfterSaleRuleVOS = RpcResultUtil.handle(orderAfterSaleRuleQueryProvider.queryByTenantId(tenantId));

        // 默认运费规则
        OrderAfterSaleRuleResp defaultOrderAfterSaleRuleVO = null;
        // 非默认
        OrderAfterSaleRuleResp orderAfterSaleRuleVO = null;

        for (OrderAfterSaleRuleResp afterSaleRuleVO : orderAfterSaleRuleVOS) {
            // 默认运费规则
            if (OrderAfterSaleRuleEnums.DefaultFlag.TRUE.getFlag().equals(afterSaleRuleVO.getDefaultFlag())) {
                defaultOrderAfterSaleRuleVO = afterSaleRuleVO;
            } else if (OrderAfterSaleRuleEnums.DefaultFlag.FALSE.getFlag().equals(afterSaleRuleVO.getDefaultFlag())) {
                orderAfterSaleRuleVO = afterSaleRuleVO;
            }
        }

        if (Objects.isNull(defaultOrderAfterSaleRuleVO)) {
            throw new DefaultServiceException("默认售后规则为空，请检查租户默认售后规则配置");
        }
        String rule = defaultOrderAfterSaleRuleVO.getRule();
        OrderAfterSaleRuleDetailVO defaultOrderAfterSaleRuleDetailVO = JSONObject.parseObject(rule, OrderAfterSaleRuleDetailVO.class);
        // 可申请售后时间
        Integer applyEndTime = defaultOrderAfterSaleRuleDetailVO.getApplyEndTime();
        // 自动完成时间
        Integer autoFinishedTime = defaultOrderAfterSaleRuleDetailVO.getAutoFinishedTime();
        if (Objects.nonNull(orderAfterSaleRuleVO)) {
            OrderAfterSaleRuleEnums.Type type = OrderAfterSaleRuleEnums.Type.getByCode(orderAfterSaleRuleVO.getType());
            List<OrderAfterSaleRuleDetailVO> orderAfterSaleRuleDetailVOS = JSONObject
                    .parseArray(orderAfterSaleRuleVO.getRule(), OrderAfterSaleRuleDetailVO.class);
            List<OrderAfterSaleRuleDetailVO> matchOrderAfterSaleRuleDetailVos = new ArrayList<>();
            switch (type) {
                // 仓
                case WAREHOUSE:
                    matchOrderAfterSaleRuleDetailVos = orderAfterSaleRuleDetailVOS.stream()
                            .filter(orderAfterSaleRuleDetailVO -> deliveryType.equals(orderAfterSaleRuleDetailVO.getDeliveryType())).collect(Collectors.toList());
                    break;
                // 商品分组
                case CLASSIFICATION:
                    matchOrderAfterSaleRuleDetailVos = orderAfterSaleRuleDetailVOS.stream()
                            .filter(orderAfterSaleRuleDetailVO -> orderAfterSaleRuleDetailVO.getClassificationIds().contains(classificationId)).collect(Collectors.toList());
                    break;
                case NULL_ERROR:
                    break;
            }

            if (!CollectionUtils.isEmpty(matchOrderAfterSaleRuleDetailVos)) {
                OrderAfterSaleRuleDetailVO orderAfterSaleRuleDetailVO = matchOrderAfterSaleRuleDetailVos
                        .get(NumberConstant.ZERO);
                // 可申请售后时间
                applyEndTime = orderAfterSaleRuleDetailVO.getApplyEndTime();
                // 自动完成时间
                autoFinishedTime = orderAfterSaleRuleDetailVO.getAutoFinishedTime();
            }
        }

        OrderItemAfterSaleRuleDTO orderItemAfterSaleRuleDTO = new OrderItemAfterSaleRuleDTO();
        orderItemAfterSaleRuleDTO.setApplyEndTime(applyEndTime);
        orderItemAfterSaleRuleDTO.setAutoFinishedTime(autoFinishedTime);
        return orderItemAfterSaleRuleDTO;
    }


    /**
     * 外部订单号加锁
     *
     * @param keys
     * @param customerOrderIdList
     */
    private void lockOrder(List<String> keys, List<String> customerOrderIdList) {
        for (String customerOrderId : customerOrderIdList) {
            String redisKey = RedisKeyEnum.C00006.join(customerOrderId);
            RLock lock = redissonClient.getLock(redisKey);
            // 未获取到锁，退出
            if (!lock.tryLock()) {
                throw new BizException(customerOrderId + "正在下单，请稍后重试");
            }
            keys.add(redisKey);
        }
    }

    /**
     * 外部订单号解锁
     *
     * @param keys
     */
    private void unlockOrder(List<String> keys) {
        for (String key : keys) {
            RLock lock = redissonClient.getLock(key);
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }


}
