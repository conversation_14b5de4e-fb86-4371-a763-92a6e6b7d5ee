package com.cosfo.mall.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.config.qiniu.QiNiuConfig;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.model.BatchStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/27
 */
@Component
@Slf4j
public class QiNiuUtils {

    @Autowired
    private QiNiuConfig config;
    private static QiNiuConfig qiNiuConfig;

    //初始化静态参数
    //通过@PostConstruct实现初始化bean之前进行的操作
    @PostConstruct
    public void init() {
        qiNiuConfig = config;
    }

    // 上传图片
    public static String uploadFile(MultipartFile file, String fileName) throws IOException {
        try {
            // 调用put方法上传
            Response res = qiNiuConfig.getUploadManager().put(file.getBytes(), fileName, qiNiuConfig.getUpToken());
            // 打印返回的信息
            if (res.isOK() && res.isJson()) {
                // 返回这张存储照片的地址
                return JSONObject.parseObject(res.bodyString()).get("key").toString();

            } else {
                log.error("七牛云上传异常:" + res.bodyString());
                return null;
            }
        } catch (QiniuException e) {
            // 请求失败时打印的异常的信息
            log.error("七牛云上传异常:{}", e.getMessage(), e);
            return null;
        }
    }

    // 下载图片
    public static File downloadFile(String url, String fileName) {
        // 获取文件后缀
        String fileSuffix = FileUtil.getFileSuffix(fileName);
        File file = FileUtil.getFileFromUrl(url + fileName, fileSuffix);
        return file;
    }

    public static void uploadPhoto(String url, String fileName) {
        String filePath = null;
        try {
            // 下载图片
            File file = downloadFile(url, fileName);
            filePath = file.getPath();
            MultipartFile multipartFile = FileUtil.fileToMultipartFile(file);
            String s = uploadFile(multipartFile, fileName);
        } catch (Exception e) {
            log.error("七牛云上传异常:{}", url, e);
        }finally {
            FileUtil.deleteFile(filePath);
        }
    }

    /**
     * 复制图片
     * @param keyList
     */
    public static void copyFileBatch(String[] keyList) {
        try {
            //单次批量请求的文件数量不得超过1000
            BucketManager.BatchOperations batchOperations = new BucketManager.BatchOperations();
            for (String key : keyList) {
                batchOperations.addCopyOp(qiNiuConfig.getXM_BUCKETNAME(), key, qiNiuConfig.getBucketname(), key);
            }
            Response response = qiNiuConfig.getBucketManager().batch(batchOperations);
            BatchStatus[] batchStatusList = response.jsonToObject(BatchStatus[].class);
            for (int i = 0; i < keyList.length; i++) {
                BatchStatus status = batchStatusList[i];
                String key = keyList[i];
                if (status.code == 200) {
                    log.info(key + " copy success");
                } else {
                    log.error(key + "copy错误，错误信息=" + status.data.error);
                }
            }
        } catch (QiniuException ex) {
            log.error("七牛云copy异常:" + ex.response, ex);
        }
    }
}
