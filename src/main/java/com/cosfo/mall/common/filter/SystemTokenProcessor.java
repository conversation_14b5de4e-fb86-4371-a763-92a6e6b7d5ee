package com.cosfo.mall.common.filter;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.utils.JwtUtils;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 用来生成系统token，主要是用于自动化测试和本地调试
 */
@Slf4j
@Component
public class SystemTokenProcessor implements InitializingBean {

    private static final long SYSTEM_TOKEN_EXPIRY_TIME = TimeUnit.DAYS.toMillis(14);

    //{"tenantId":2,"storeId":4262,"accountId":3532,"expirationTime":********}
    @Value("${system.token.default:{\"tenantId\":2,\"storeId\":4262,\"accountId\":3532,\"expirationTime\":********}}")
    private String systemToken;

    @Value("${system.token.enabled:false}")
    private boolean enabled;

    @Value("${arms.appName:test-cosfo-mall}")
    private String armsAppName;

    public boolean isEnabled() {
        return enabled;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("About to generate system token for:{}", systemToken);
        try {
            generateSystemToken();
        } catch (Throwable t) {
            //ignore any exceptions.
            log.error("generate system_token failed:", t);
        }
    }

    /**
     * 支持用旧token换取新的token，
     *
     * @param token                                 ：旧token
     * @param newStoreId：新的store                    ID，和旧token同一个tenant；
     * @param newAccountId：新的账户ID，和旧token同一个tenant；
     * @return
     */
    public String exchangeToken(String token, Long newStoreId, Long newAccountId) throws Exception {
        if (!isEnabled()) {
            return null;
        }
        Map<String, Object> oldTokenInfo = JwtUtils.validateTokenAndGetClaims(token);
        if (CollectionUtils.isEmpty(oldTokenInfo)) {
            return null;
        }
        Long tenantId = Long.valueOf(oldTokenInfo.get(JwtUtils.TENANT_ID).toString());
        if (null == newStoreId) {
            newStoreId = Long.valueOf(oldTokenInfo.get(JwtUtils.STORE_ID).toString());
        }
        if (null == newAccountId) {
            newAccountId = Long.valueOf(oldTokenInfo.get(JwtUtils.ACCOUNT_ID).toString());
        }

        return JwtUtils.generateSystemToken(tenantId, newStoreId, newAccountId, SYSTEM_TOKEN_EXPIRY_TIME);
    }


    private void generateSystemToken() throws Exception {
        if (!enabled) {
            log.warn("system token is disabled.");
            return;
        }
        if (!"test-cosfo-mall".equalsIgnoreCase(armsAppName)) {
            log.warn("system token is disabled on this env:{}", armsAppName);
            return;
        }
        SystemTokenData data = JSON.parseObject(systemToken, SystemTokenData.class);
        if (null == data || null == data.accountId) {
            log.error("unable to generate system_token for value:{}", systemToken);
            return;
        }

        String token = JwtUtils.generateSystemToken(data.tenantId, data.storeId, data.accountId, SYSTEM_TOKEN_EXPIRY_TIME);
        log.warn("new_system_token:{}", token);
    }

    @Data
    public static class SystemTokenData {

        private Long tenantId;
        private Long storeId;
        private Long accountId;
        private String systemTokenHost;
    }
}
