package com.cosfo.mall.common.factory;

import com.cosfo.mall.common.constants.PayCodeEnum;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.payment.template.RefundTemplate;
import com.cosfo.mall.payment.template.huifu.alipay.HuiFuAliRefund;
import com.cosfo.mall.payment.template.huifu.wechat.HuiFuWechatRefund;
import com.cosfo.mall.payment.template.nativepay.*;
import com.cosfo.mall.payment.template.wechat.WechatRefund;
import io.jsonwebtoken.lang.Collections;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 退款策略上下文
 * @date 2023/3/16 11:22
 */
@Component
public class RefundStrategyFactory {

    private Map<String, RefundTemplate> refundChannelMap;
    private Map<String, RefundTemplate> refundChannelByTradeTypeMap;

    @Resource
    private WechatRefund wechatRefund;

    @Resource
    private HuiFuWechatRefund huiFuWechatRefund;
    @Resource
    private HuiFuAliRefund huiFuAliRefund;
    @Resource
    private BillRefund billRefund;
    @Resource
    private BalanceRefund balanceRefund;
    @Resource
    private ZeroPriceRefund zeroPriceRefund;
    @Resource
    private OfflineRefund offlineRefund;
    @Resource
    private NonCashRefund nonCashRefund;

    @PostConstruct
    private void initRefundChannelMap() {
        refundChannelMap = new HashMap<>(5);
        refundChannelMap.put(PayCodeEnum.WECHAT_PAY.getCode(), wechatRefund);
        refundChannelMap.put(PayCodeEnum.BILL_PAY.getCode(), billRefund);
        refundChannelMap.put(PayCodeEnum.BALANCE_PAY.getCode(), balanceRefund);
        refundChannelMap.put(PayCodeEnum.HF_WECHAT_PAY.getCode(), huiFuWechatRefund);
        refundChannelMap.put(PayCodeEnum.HF_ALI_PAI.getCode(), huiFuAliRefund);
        refundChannelMap.put(PayCodeEnum.ZERO_PRICE_PAY.getCode(), zeroPriceRefund);
        refundChannelMap.put(PayCodeEnum.OFFLINE_PAY.getCode(), offlineRefund);
        refundChannelMap.put(PayCodeEnum.NON_CASH_PAY.getCode(), nonCashRefund);
    }

    @PostConstruct
    private void initTradeTypePayChannelMap() {
        refundChannelByTradeTypeMap = new HashMap<>(6);
        // key = order.payType + order.onlinePaymentChannel
        refundChannelByTradeTypeMap.put(TradeTypeEnum.JSAPI.getDesc(), wechatRefund);
        refundChannelByTradeTypeMap.put(TradeTypeEnum.T_JSAPI.getDesc(), huiFuWechatRefund);
        refundChannelByTradeTypeMap.put(TradeTypeEnum.T_MINIAPP.getDesc(), huiFuWechatRefund);
        refundChannelByTradeTypeMap.put(TradeTypeEnum.A_NATIVE.getDesc(), huiFuWechatRefund);
        refundChannelByTradeTypeMap.put(TradeTypeEnum.BILL.getDesc(), billRefund);
        refundChannelByTradeTypeMap.put(TradeTypeEnum.BALANCE.getDesc(), balanceRefund);
        refundChannelByTradeTypeMap.put(TradeTypeEnum.ZERO_PRICE.getDesc(), zeroPriceRefund);
        refundChannelByTradeTypeMap.put(TradeTypeEnum.HF_WECHAT_PLUGIN.getDesc(), huiFuWechatRefund);
        refundChannelByTradeTypeMap.put(TradeTypeEnum.OFFLINE_PAY.getDesc(), offlineRefund);
        refundChannelByTradeTypeMap.put(TradeTypeEnum.NON_CASH_PAY.getDesc(), nonCashRefund);
    }

    public RefundTemplate getTemplateByTradeType(String tradeType, BigDecimal refundPrice) {
        if (Collections.isEmpty(refundChannelByTradeTypeMap)) {
            throw new ProviderException("策略实例集合初始化失败，请检查是否正确注入");
        }
        if (refundPrice.compareTo(BigDecimal.ZERO) == 0) {
            return zeroPriceRefund;
        }
        RefundTemplate refundTemplate = this.refundChannelByTradeTypeMap.get(tradeType);
        if (refundTemplate == null) {
            throw new ProviderException("未找到对应的退款策略");
        }
        return this.refundChannelByTradeTypeMap.get(tradeType);
    }

    /**
     * 获取退款渠道
     *
     * @param refundCode 退款码 = order.payType + order.onlinePaymentChannel
     * @return {@link RefundTemplate}
     */
    public RefundTemplate getRefundChannel(String refundCode) {
        if (Collections.isEmpty(refundChannelMap)) {
            throw new ProviderException("策略实例集合初始化失败，请检查是否正确注入");
        }
        RefundTemplate refundTemplate = this.refundChannelMap.get(refundCode);
        if (refundTemplate == null) {
            throw new ProviderException("未找到对应的退款策略");
        }
        return this.refundChannelMap.get(refundCode);
    }

}
