package com.cosfo.mall.common.factory;

import com.cosfo.mall.common.constants.PayCodeEnum;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.common.context.PaymentEnums;
import com.cosfo.mall.payment.strategy.PayStrategy;
import com.cosfo.mall.payment.strategy.impl.*;
import com.cosfo.mall.payment.template.CombinedPayment;
import com.cosfo.mall.payment.template.PayTemplate;
import com.cosfo.mall.payment.template.huifu.alipay.HuiFuAliPayment;
import com.cosfo.mall.payment.template.huifu.plugin.HuiFuWechatPluginPayment;
import com.cosfo.mall.payment.template.huifu.wechat.HuiFuWechatPayment;
import com.cosfo.mall.payment.template.nativepay.*;
import com.cosfo.mall.payment.template.wechat.WechatPayment;
import io.jsonwebtoken.lang.Collections;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @desc 支付策略上下文
 * <AUTHOR>
 * @date 2023/3/16 11:22
 */
@Component
public class PayStrategyFactory {


    /**
     * 策略实例集合
     */
    private Map<String, PayStrategy> payStrategyMap;

    private Map<String, PayTemplate> payChannelMap;
    private Map<String, PayTemplate> payChannelByTradeTypeMap;

    @Resource
    private WeiXinPayStrategy weiXinPayStrategy;
    @Resource
    private HuiFuPayStrategy huiFuPayStrategy;
    @Resource
    private BillPayStrategy billPayStrategy;
    @Resource
    private BalancePayStrategy balancePayStrategy;
    @Resource
    private OfflinePayStrategy offlinePayStrategy;
    @Resource
    private NonCashPayStrategy nonCashPayStrategy;
    @Resource
    private CombinedPayStrategy combinedPayStrategy;
    @Resource
    private WechatPayment wechatPayment;
    @Resource
    private HuiFuWechatPayment huiFuWechatPayment;
    @Resource
    private HuiFuWechatPluginPayment huiFuWechatPluginPayment;
    @Resource
    private HuiFuAliPayment huiFuAliPayment;
    @Resource
    private BillPayment billPayment;
    @Resource
    private BalancePayment balancePayment;
    @Resource
    private ZeroPricePayment zeroPricePayment;
    @Resource
    private OfflinePayment offlinePayment;
    @Resource
    private NonCashPayment nonCashPayment;
    @Resource
    private CombinedPayment combinedPayment;

    @PostConstruct
    private void initPayStrategyMap() {
        payStrategyMap = new HashMap<>(5);
        payStrategyMap.put(PaymentEnums.WX_PAY.getPayCode(), weiXinPayStrategy);
        payStrategyMap.put(PaymentEnums.HF_PAY.getPayCode(), huiFuPayStrategy);
        payStrategyMap.put(PaymentEnums.BILL_PAY.getPayCode(), billPayStrategy);
        payStrategyMap.put(PaymentEnums.BALANCE_PAY.getPayCode(), balancePayStrategy);
        payStrategyMap.put(PaymentEnums.OFFLINE_PAY.getPayCode(), offlinePayStrategy);
        payStrategyMap.put(PaymentEnums.NON_CASH_PAY.getPayCode(), nonCashPayStrategy);
        payStrategyMap.put(PaymentEnums.COMBINED_PAY.getPayCode(), combinedPayStrategy);
    }

    /**
     * 选择支付方式
     * 微信、汇付、账期、余额
     *
     * @param payCode
     * @return PayStrategy
     *
     * @return RemoteResult
     */
    public PayStrategy getStrategy(String payCode) {
        if (Collections.isEmpty(payStrategyMap)) {
            throw new ProviderException("策略实例集合初始化失败，请检查是否正确注入");
        }
        return this.payStrategyMap.get(payCode);
    }

    @PostConstruct
    private void initPayChannelMap() {
        payChannelMap = new HashMap<>(10);
        // key = order.payType + order.onlinePaymentChannel
        payChannelMap.put(PayCodeEnum.WECHAT_PAY.getCode(), wechatPayment);
        payChannelMap.put(PayCodeEnum.BILL_PAY.getCode(), billPayment);
        payChannelMap.put(PayCodeEnum.BALANCE_PAY.getCode(), balancePayment);
        payChannelMap.put(PayCodeEnum.HF_WECHAT_PAY.getCode(), huiFuWechatPayment);
        payChannelMap.put(PayCodeEnum.HF_ALI_PAI.getCode(), huiFuAliPayment);
        payChannelMap.put(PayCodeEnum.ZERO_PRICE_PAY.getCode(), zeroPricePayment);
        payChannelMap.put(PayCodeEnum.HF_WECHAT_PLUGIN_PAY.getCode(), huiFuWechatPluginPayment);
        payChannelMap.put(PayCodeEnum.OFFLINE_PAY.getCode(), offlinePayment);
        payChannelMap.put(PayCodeEnum.NON_CASH_PAY.getCode(), nonCashPayment);
        payChannelMap.put(PayCodeEnum.COMBINED_PAY_WX.getCode(), combinedPayment);
        payChannelMap.put(PayCodeEnum.COMBINED_PAY_HF.getCode(), combinedPayment);
        payChannelMap.put(PayCodeEnum.COMBINED_PAY_HF_PLUGIN.getCode(), combinedPayment);
    }

    @PostConstruct
    private void initTradeTypePayChannelMap() {
        payChannelByTradeTypeMap = new HashMap<>(10);
        // key = order.payType + order.onlinePaymentChannel
        payChannelByTradeTypeMap.put(TradeTypeEnum.JSAPI.getDesc(), wechatPayment);
        payChannelByTradeTypeMap.put(TradeTypeEnum.T_JSAPI.getDesc(), huiFuWechatPayment);
        payChannelByTradeTypeMap.put(TradeTypeEnum.T_MINIAPP.getDesc(), huiFuWechatPayment);
        payChannelByTradeTypeMap.put(TradeTypeEnum.A_NATIVE.getDesc(), huiFuAliPayment);
        payChannelByTradeTypeMap.put(TradeTypeEnum.BILL.getDesc(), billPayment);
        payChannelByTradeTypeMap.put(TradeTypeEnum.BALANCE.getDesc(), balancePayment);
        payChannelByTradeTypeMap.put(TradeTypeEnum.HF_WECHAT_PLUGIN.getDesc(), huiFuWechatPluginPayment);
        payChannelByTradeTypeMap.put(TradeTypeEnum.OFFLINE_PAY.getDesc(), offlinePayment);
        payChannelByTradeTypeMap.put(TradeTypeEnum.NON_CASH_PAY.getDesc(), nonCashPayment);
        payChannelByTradeTypeMap.put(TradeTypeEnum.COMBINED_PAY.getDesc(), combinedPayment);
    }

    public PayTemplate getTemplateByTradeType(String tradeType) {
        if (Collections.isEmpty(payChannelByTradeTypeMap)) {
            throw new ProviderException("策略实例集合初始化失败，请检查是否正确注入");
        }
        PayTemplate payTemplate = this.payChannelByTradeTypeMap.get(tradeType);
        if (payTemplate == null) {
            throw new ProviderException("未找到对应的支付策略");
        }
        return this.payChannelByTradeTypeMap.get(tradeType);
    }

    /**
     * 获取支付渠道
     *
     * @param payCode 支付码 = order.payType + order.onlinePaymentChannel
     * @return {@link PayTemplate}
     */
    public PayTemplate getPayChannel(String payCode) {
        if (Collections.isEmpty(payChannelMap)) {
            throw new ProviderException("策略实例集合初始化失败，请检查是否正确注入");
        }
        PayTemplate payTemplate = this.payChannelMap.get(payCode);
        if (payTemplate == null) {
            throw new ProviderException("未找到对应的支付策略");
        }
        return this.payChannelMap.get(payCode);
    }

}
