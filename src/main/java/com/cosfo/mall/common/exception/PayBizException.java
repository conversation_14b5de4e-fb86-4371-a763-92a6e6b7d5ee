package com.cosfo.mall.common.exception;

import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.error.code.BizErrorCode;
import net.xianmu.common.exception.error.code.ErrorCode;


/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/25 17:28
 */
public class PayBizException extends BizException {

    /**
     * errorCode
     */
    private ErrorCode errorCode = new BizErrorCode();

    /**
     * 数据
     */
    private Object data;

    public PayBizException() {}

    public PayBizException(String message) {
        super(message);
    }

    public PayBizException(String message, ErrorCode errorCode, Object data) {
        super(message);
        this.errorCode = errorCode;
        this.data = data;
    }

    public Object getData() {
        return this.data;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }
}
