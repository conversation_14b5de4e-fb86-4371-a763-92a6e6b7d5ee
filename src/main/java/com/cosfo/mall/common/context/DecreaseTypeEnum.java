package com.cosfo.mall.common.context;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-04-27
 **/
public enum DecreaseTypeEnum {
    /**
     * 扣减
     */
    DECREASE(1, "扣减"),

    /**
     * 冻结
     */
    FREEZE(2, "冻结"),

    /**
     * 释放
     */
    RELEASE(3, "释放");

    private final Integer type;

    private final String desc;

    DecreaseTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
