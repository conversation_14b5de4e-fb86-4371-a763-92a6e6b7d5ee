package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-04-21
 **/
public class MerchantStoreBalanceEnums {

    /**
     * 账户类型 0=现金余额，1=非现金账户
     */
    @Getter
    @AllArgsConstructor
    public enum AccountTypeEnum {

        CASH(0, "现金余额"),
        NON_CASH(1, "非现金账户");

        private final Integer type;
        private final String message;
    }

    @Getter
    @AllArgsConstructor
    public enum DecreaseTypeEnum {

        /**
         * 扣减
         */
        DECREASE(1, "扣减"),

        /**
         * 冻结
         */
        FREEZE(2, "冻结"),

        /**
         * 释放
         */
        RELEASE(3, "释放");

        private final Integer type;

        private final String desc;
    }
}
