package com.cosfo.mall.common.constant;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/1/11 15:02
 */
public class MqGroupConstant {

    /**
     * 新的延时消息group
     */
    public static final String GID_COSFO_MALL_DELAY = "GID_cosfo_mall_delay";

    /**
     * 鲜沐saas消费组
     */
    public static final String GID_SUMMERFARM_TO_COSFO = "GID_summerfarm_to_cosfo";

    /**
     * ofc saas消费组 order
     */
    public static final String GID_OFC_TO_COSFO_ORDER = "GID_ofc_to_cosfo_order";

    /**
     * ofc saas新消费组 order
     */
    public static final String GID_OFC_TO_COSFO_ORDER_NEW = "GID_ofc_to_cosfo_order_new";
    /**
     * ofc saas消费组 order
     */
    public static final String GID_OFC_TO_COSFO_AFTERSALE = "GID_ofc_to_cosfo_aftersale";
    /**
     * ofc saas新消费组 order
     */
    public static final String GID_OFC_TO_COSFO_AFTERSALE_NEW = "GID_ofc_to_cosfo_aftersale_new";
    /**
     * ofc saas消费组 售后物流
     */
    public static final String GID_OFC_TO_COSFO_DELIVERY4AFTERSALE = "GID_ofc_to_cosfo_delivery4aftersale";
    /**
     * ofc saas消费组 售后补发出库
     */
    public static final String GID_OFC_TO_COSFO_RESENDSUCCESSAFTERSALE = "GID_ofc_to_cosfo_resendsuccessaftersale";
 /**
     * ofc saas消费组
  * 配送前售后退款
  *  - 售后退款单成功通知消息
     */
    public static final String GID_OFC_TO_COSFO_FULFILLMENT_ORDER_RESULT = "GID_ofc_to_cosfo_fulfillment_order_result";

    /**
     * tms saas消费组 order
     */
    public static final String GID_ORDER_CHANGE = "GID_saas_mall_order_pay_change";

    /**
     * 自营订单占用
     */
    public static final String GID_SELF_SUPPLY_ORDER_OCCUPY = "GID_saas_self_supply_order_occupy";

    /**
     * 订单出库完成租
     */
    public static final String GID_ORDER_WAREHOUSING_FINISHED = "GID_saas_order_warehousing_finished";

    /**
     * ofc履约创建消费者组
     */
    public static final String GID_OFC_TO_COSFO_ORDER_FULFILLMENT_CREATE = "GID_ofc_to_cosfo_order_fulfillment_create";

    /**
     * 三方仓出库任务创建提醒
     */
    public static final String GID_OFC_TO_COSFO_OUTBOUND_TASK_CREATED_NOTICE = "GID_ofc_to_cosfo_outbound_task_created_notice";

    /**
     * 三方仓订单配送后售后回告配送日期和城配仓号
     */
    public static final String GID_OFC_TO_COSFO_AFTER_DELIVERY_AFTER_SALE_ORDER = "GID_ofc_saas_after_delivery_after_sale_order";

    /**
     * 监听cosfo库中的表数据,消费流转业务
     */
    public static final String MYSQL_BINLOG_COSFO_MALL = "GID_saas_mall_cosfo_common_binlog";

    /**
     * 监听分账
     */
    public static final String GID_BILL_PROFIT_SHARING = "GID_trade_common";

    /**
     * 订单相关
     */
    public static final String GID_ORDER_PROCESS = "GID_order_process";

    /**
     * 清结算相关
     */
    public static final String GID_SETTLEMENT_PROCESS = "GID_settlement_process";
}
