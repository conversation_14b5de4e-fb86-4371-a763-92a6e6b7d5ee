package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.order.model.dto.AfterSaleFinishDeliveryDTO;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.cosfo.SaasMQData;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Deprecated
@Slf4j
@Component
@MqListener(topic = "topic_ofc_delivery",
        consumerGroup = MqGroupConstant.GID_OFC_TO_COSFO_AFTERSALE,
        tag = "tag_cosfo_after_sale_message"
)
public class OFCAfterSaleListener extends AbstractMqListener<SaasMQData> {
    public static final String AFTER_SALE_SEND_FINISH = "AFTER_SALE_SEND_FINISH";
    @Resource
    private OrderAfterSaleService orderAfterSaleService;

    @Override
    public void process(SaasMQData msg) {
        log.info("rocketmq 收到OFC消息，消息内容：{}", JSONObject.toJSONString(msg));
        long startTime = System.currentTimeMillis();
        log.info("线程{}：MQ开始回调执行时间：{}ms", Thread.currentThread().getName(), startTime);
        String msgType = msg.getMsgType();
        String jsonString = (String)msg.getMsgData();
        switch (msgType) {
            case AFTER_SALE_SEND_FINISH:
                // 售后任务处理结果
                List<AfterSaleFinishDeliveryDTO> list = JSONObject.parseArray(jsonString, AfterSaleFinishDeliveryDTO.class);
                orderAfterSaleService.afterSaleProcessFinish(list);
                break;
            default:
                log.error("消息类型异常，消息内容：{}", JSONObject.toJSONString(msg));
                break;
        }


        long endTime = System.currentTimeMillis();
        log.info("线程{}：MQ开始回调结束时间：{}ms", Thread.currentThread().getName(), endTime);
    }
}
