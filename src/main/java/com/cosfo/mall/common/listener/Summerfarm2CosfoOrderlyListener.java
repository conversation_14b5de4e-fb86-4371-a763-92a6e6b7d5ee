package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.SummerfarmMqGroup;
import com.cosfo.summerfarm.mq.msg.SummerfarmMqTag;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

/**
 * @desc 鲜沐、SaaS交互顺序队列
 * <AUTHOR>
 */
@Slf4j
@Component
@MqOrderlyListener(topic = SummerfarmMQTopic.SUMMERFARM_TO_COSFO_MALL,
        consumerGroup = SummerfarmMqGroup.GID_SUMMERFARM_TO_COSFO,
        tag = SummerfarmMqTag.SUMMERFARM_TO_COSFO
)
public class Summerfarm2CosfoOrderlyListener extends AbstractMqListener<SummerfarmMsgModel> {
    @Override
    public void process(SummerfarmMsgModel msg) {
        log.info("rocketmq 收到summerfarm消息，升级mq扩展包版本,消息内容：{}", JSONObject.toJSONString(msg));
        //处理消息
    }
}
