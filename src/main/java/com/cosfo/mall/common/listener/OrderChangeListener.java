package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.summerfarm.model.dto.SummerFarmOrderPayResultDTO;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.msg.SummerfarmMqTag;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述: 订单生成配送计划消息通知
 * 慢慢废弃
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/20
 */
@Slf4j
@Component
@MqListener(topic = SummerfarmMQTopic.SAAS_ORDER_CHANGE_RESULT,
        consumerGroup = MqGroupConstant.GID_ORDER_CHANGE,
        tag = SummerfarmMqTag.ORDER_PAY_CHANGE
)
@Deprecated
public class OrderChangeListener extends AbstractMqListener<SummerFarmOrderPayResultDTO> {
    @Resource
    private OrderService orderService;

    @Override
    public void process(SummerFarmOrderPayResultDTO summerFarmOrderPayResultDTO) {
        log.warn("rocketmq 收到tms消息，消息内容：{}", JSONObject.toJSONString(summerFarmOrderPayResultDTO));

//        orderService.orderWaitingDeliveryNotify(summerFarmOrderPayResultDTO);
    }
}
