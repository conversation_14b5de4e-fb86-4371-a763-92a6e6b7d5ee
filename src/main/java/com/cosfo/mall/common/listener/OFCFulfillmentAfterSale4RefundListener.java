package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.mall.stock.service.StockService;
import com.cosfo.mall.tenant.service.SpecialTenantService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleUpdateReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.cosfo.FulfillmentOrderResultMessageDTO;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 配送前售后退款，履约单创建结果消息
 * - 售后退款单成功通知消息
 * - 无仓、自营仓、三方仓售后单都处理
 */
@Slf4j
@Component
@MqListener(topic = "topic_ofc_fulfillment_order_result",
        consumerGroup = MqGroupConstant.GID_OFC_TO_COSFO_FULFILLMENT_ORDER_RESULT,
        tag = "tag_ofc_saas_after_sale_order"
)
public class OFCFulfillmentAfterSale4RefundListener extends AbstractMqListener<FulfillmentOrderResultMessageDTO> {
    @Resource
    private RefundService refundService;
    @Resource
    private StockService stockService;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;
    @Resource
    private SpecialTenantService specialTenantService;

    @Override
    public void process(FulfillmentOrderResultMessageDTO msg) {
        log.info("rocketmq 收到OFC售后退款单成功通知消息，消息内容：{}", JSONObject.toJSONString(msg));
        String sourceOrderNo = msg.getAfterSaleOrderNo();
        if (StringUtil.isEmpty(sourceOrderNo)) {
            return;
        }
        if (!msg.isOfcHandleSuccess()) {
            log.error("rocketmq 收到OFC售后退款单通知消息，处理失败，消息内容：{}", JSONObject.toJSONString(msg));
            return;
        }

        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByNos(Lists.newArrayList(sourceOrderNo)));

        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            log.error("rocketmq 收到OFC售后退款单通知消息，售后单为空，消息内容：{}", JSONObject.toJSONString(msg));
            return;
        }

        OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);

        // 售后状态不为【2处理中】，针对重复消息，幂等处理
        if (!OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue().equals(afterSaleDTO.getStatus())) {
            log.error("售后单状态不是[处理中],重复消息,无需再次发起退款,orderAfterSale:{}", JSON.toJSONString(afterSaleDTO));
            return;
        }

        // 是否需要释放库存
        boolean needReleaseInventory = !specialTenantService.cancelOrderNotNeedReleaseInventory(afterSaleDTO.getTenantId());

        if (needReleaseInventory){
            // 库存解锁
            stockService.unlockStockByAfterSale(afterSaleDTO);
        }


        // 更新售后单状态【退款中】
        OrderAfterSaleUpdateReq update = new OrderAfterSaleUpdateReq();
        update.setId(afterSaleDTO.getId());
        update.setStatus(OrderAfterSaleStatusEnum.REFUNDING.getValue());
        RpcResultUtil.handle(orderAfterSaleCommandProvider.updateById(update));
        RefundDTO refundDTO = new RefundDTO();
        refundDTO.setAfterSaleId(afterSaleDTO.getId());
        refundDTO.setOrderId(afterSaleDTO.getOrderId());
        refundDTO.setTenantId(afterSaleDTO.getTenantId());
        refundDTO.setRefundPrice(afterSaleDTO.getTotalPrice());
        refundService.refundRequest(refundDTO);


        long endTime = System.currentTimeMillis();
        log.info("线程{}：MQ开始回调结束时间：{}ms", Thread.currentThread().getName(), endTime);
    }
}
