package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.common.constant.MQType;
import com.cosfo.mall.common.constants.OrderCancelTypeEnum;
import com.cosfo.mall.common.mq.model.DelayData;
import com.cosfo.mall.order.model.dto.OrderCancelDTO;
import com.cosfo.mall.order.model.vo.OrderVO;
import com.cosfo.mall.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 延时消息
 * @createTime 2021年11月26日 14:45:00
 */
@Slf4j
@Component
@MqListener(
        topic = MQTopicConstant.MALL_DELAY_LIST,
        consumerGroup = MQTopicConstant.GID_MALL_DELAY,
        maxReconsumeTimes = 5
)
public class DelayManageListListener extends AbstractMqListener<DelayData>   {
    @Resource
    private OrderService orderService;
    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;

    @Override
    public void process(DelayData delayData) {
        log.info("延时消息rocketmq receive：{}", delayData);
        String data = delayData.getData();
        String msgType = delayData.getType();
        try {
            switch (msgType) {
                case MQType.ORDER_TIMEOUT_CLOSE:
                    OrderVO orderVo = JSONObject.parseObject(data, OrderVO.class);
                    log.info("订单超时取消处理：{}", orderVo);
                    orderService.cancel(orderVo.getOrderId(), null, OrderCancelTypeEnum.NORMAL_CANCEL.getType());
                    break;
                case MQType.ORDER_TIMEOUT_CLOSE_V2:
                    OrderCancelDTO orderCancelDTO = JSONObject.parseObject(data, OrderCancelDTO.class);
                    log.info("订单超时取消处理V2：{}", orderCancelDTO);
                    orderService.cancel(orderCancelDTO.getOrderId(), null, orderCancelDTO.getCancelType());
                    break;
                default:
                    log.error("消息类型异常，消息内容：{}", delayData);
                    break;
            }

        } catch (Exception e) {
            log.error("消费延迟消息失败:{}", delayData, e);
        }
    }
}
