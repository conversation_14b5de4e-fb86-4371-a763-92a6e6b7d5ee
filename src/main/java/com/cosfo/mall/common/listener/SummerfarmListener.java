package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgType;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-10-14
 * @description mall-list topic监听
 */

@Slf4j
@Component
@MqOrderlyListener(
        topic = SummerfarmMQTopic.SUMMRFARM_TO_SAAS,
        consumerGroup = "GID_saas-mall",
        tag = "*")
public class SummerfarmListener extends AbstractMqListener<SummerfarmMsgModel> {

    @Override
    public void process(SummerfarmMsgModel msg) {
        String msgType = msg.getMsgType();
        String jsonString = JSON.toJSONString(msg.getMsgData());
        switch (msgType) {
            case SummerfarmMsgType.SKU_SYNCHRONIZED:
                break;
            case SummerfarmMsgType.AFTER_SALE_DELIVERY:
                // 售后任务处理结果
                break;
            default:
                log.error("消息类型异常，消息内容：{}", JSONObject.toJSONString(msg));
                break;
        }

    }
}
