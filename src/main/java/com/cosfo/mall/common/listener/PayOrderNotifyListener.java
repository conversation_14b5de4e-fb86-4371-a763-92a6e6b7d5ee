package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.payment.model.dto.PayNotifyMessageDTO;
import com.cosfo.mall.payment.service.impl.PaymentOrderNotifyServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 支付通知监听器
 * @author: George
 * @date: 2024-08-19
 **/
@Slf4j
@Component
@MqOrderlyListener(topic = MQTopicConstant.TOPIC_SAAS_PAYMENT_NOTIFY,
        consumerGroup = MqGroupConstant.GID_ORDER_PROCESS,
        maxReconsumeTimes = 3
)
public class PayOrderNotifyListener extends AbstractMqListener<PayNotifyMessageDTO> {

    @Resource
    private PaymentOrderNotifyServiceImpl paymentOrderNotifyService;

    @Override
    public void process(PayNotifyMessageDTO payNotifyMessageDTO) {
        log.info("rocketmq 收到支付通知订单消息，消息内容：{}", JSON.toJSON(payNotifyMessageDTO));
        String paymentNo = payNotifyMessageDTO.getPaymentNo();
        if (StringUtils.isBlank(paymentNo)) {
            log.error("支付通知消息处理失败，支付单号为空");
            return;
        }
        paymentOrderNotifyService.notify(paymentNo);
        log.info("支付通知订单消息处理完成，消息内容：{}", JSON.toJSON(payNotifyMessageDTO));
    }
}
