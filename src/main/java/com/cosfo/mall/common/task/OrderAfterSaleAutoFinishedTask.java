package com.cosfo.mall.common.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/18
 */
@Slf4j
@Component
public class OrderAfterSaleAutoFinishedTask extends XianMuJavaProcessorV2 {

    @Resource
    private OrderAfterSaleService orderAfterSaleService;

    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("超时售后单自动完成调度任务开始------");

        String jobParametersStr = jobContext.getJobParameters();

        JobParameters jobParameters = JSONObject.parseObject(jobParametersStr, JobParameters.class);
        orderAfterSaleService.orderAfterSaleAutoFinished();
        log.info("超时售后单自动完成调度任务结束-----");


        return new ProcessResult(true);
    }
}
