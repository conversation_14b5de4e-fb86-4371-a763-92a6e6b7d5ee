package com.cosfo.mall.common.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.bill.convert.BillProfitSharingOrderConvert;
import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Component
@Slf4j
public class OrderProfitSharingTask extends XianMuJavaProcessorV2 {
    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;
    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("订单分账任务开始执行...");
        String jobParameters = jobContext.getJobParameters();
        String instanceParameters = jobContext.getInstanceParameters();
        List<BillProfitSharingOrderDTO> billProfitSharingOrderDTOS = queryNeedProfitSharingOrder(jobParameters, instanceParameters);
        if (CollectionUtils.isEmpty(billProfitSharingOrderDTOS)) {
            log.info("未查询到需要分账的订单，订单分账任务执行完毕...");
            return new ProcessResult(true);
        }

        // 开始处理分账
        billProfitSharingOrderDTOS.forEach(el -> {
            profitSharingBusinessService.handleOrderProfitSharing(el);
        });
        log.info("订单分账任务执行完毕...");
        return new ProcessResult(true);
    }

    private List<BillProfitSharingOrderDTO> queryNeedProfitSharingOrder(String jobParameters, String instanceParameters) {
        if (!StringUtils.isEmpty(instanceParameters)) {
            ExecuteTaskParams executeTaskParams = JSON.parseObject(instanceParameters, ExecuteTaskParams.class);
            String orderId = executeTaskParams.getOrderId();
            String tenantId = executeTaskParams.getTenantId();
            String profitSharingNo = executeTaskParams.getProfitSharingNo();
            if (!StringUtils.isEmpty(profitSharingNo)) {
                BillProfitSharingOrder billProfitSharingOrder = billProfitSharingOrderService.queryByProfitSharingNo(profitSharingNo);
                return BillProfitSharingOrderConvert.convertToList(Collections.singletonList(billProfitSharingOrder));
            }
            if (StringUtils.isNoneEmpty(orderId, tenantId)) {
                List<String> orderIds = Lists.newArrayList(orderId);
                return billProfitSharingOrderService.queryByOrderIds(orderIds.stream().map(Long::parseLong).collect(Collectors.toList()), Long.valueOf(tenantId));
            }
        }
        if (!StringUtils.isEmpty(jobParameters)) {
            ExecuteTaskParams executeTaskParams = JSON.parseObject(jobParameters, ExecuteTaskParams.class);
            String maxRetryNum = executeTaskParams.getMaxRetryNum();
            if (StringUtils.isNotEmpty(maxRetryNum)) {
                return billProfitSharingOrderService.queryWaitingProfitSharingOrder(Integer.valueOf(maxRetryNum));
            }
        }
        return Collections.emptyList();
    }

    @Data
    private static class ExecuteTaskParams {

        /**
         * 分账单号
         */
        private String profitSharingNo;

        /**
         * 订单id
         */
        private String orderId;

        /**
         * 租户id
         */
        private String tenantId;

        /**
         * 最大重试次数
         */
        private String maxRetryNum;
    }

}
