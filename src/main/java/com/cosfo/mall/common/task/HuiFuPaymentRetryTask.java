package com.cosfo.mall.common.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.WxPaySceneEnum;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.HuiFuPaymentRateRetry;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.service.HuiFuPaymentRateRetryService;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.mall.wechat.api.HuiFuApi;
import com.cosfo.mall.wechat.bean.huifu.QryWxConfDTO;
import com.cosfo.mall.wechat.bean.huifu.SettleMentInfoDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-04
 * @Description: 汇付支付费率重试任务
 */
@Component
@Slf4j
public class HuiFuPaymentRetryTask extends XianMuJavaProcessorV2 {

    @Resource
    private HuiFuPaymentRateRetryService huiFuPaymentRateRetryService;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private HuiFuConfig huiFuConfig;
    @Resource
    private PaymentMapper paymentMapper;

    @Override
    public ProcessResult processResult(XmJobInput context) {
        log.info("汇付支付费率重试任务开始------调度任务:{}", context.getJobParameters());

        LocalDateTime now = LocalDateTime.now();
        // 近一个小时内的数据
        LocalDateTime startTime = now.minusHours(NumberConstant.ONE);
        LocalDateTime endTime = now.minusMinutes(NumberConstant.ONE);
        List<HuiFuPaymentRateRetry> huiFuPaymentRateRetries = huiFuPaymentRateRetryService.selectListByTime(startTime, endTime);

        for (HuiFuPaymentRateRetry huiFuPaymentRateRetry : huiFuPaymentRateRetries) {
            Long primaryId = huiFuPaymentRateRetry.getId();
            Long paymentId = huiFuPaymentRateRetry.getPaymentId();
            try {
                // 查询汇率
                long start = System.currentTimeMillis();
                TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantAuthConnectionService.selectByTenantId(huiFuPaymentRateRetry.getTenantId());
                SettleMentInfoDTO settleMentInfoDTO = HuiFuApi.queryMerchantBasicData(tenantAuthConnectionDTO, huiFuConfig);
                log.info("查询支付费率耗时:{}ms,支付单重试信息:{},返回:{}", System.currentTimeMillis() - start, JSON.toJSONString(huiFuPaymentRateRetry), JSON.toJSONString(settleMentInfoDTO));

                String feeRate = null;
                List<QryWxConfDTO> qryWxConfDTOList = settleMentInfoDTO.getQryWxConfDTO();
                for (QryWxConfDTO qryWxConfDTO : qryWxConfDTOList) {
                    if (WxPaySceneEnum.OFFLINE_MIMI_PROGRAM.getCode().equals(qryWxConfDTO.getPay_scene())) {
                        feeRate = qryWxConfDTO.getFee_rate();
                    }
                }

                if (StringUtils.isEmpty(feeRate)) {
                    throw new BizException("费率不能为空");
                }

                // 若费用不为空,更新费率,删除重试数据
                String finalFeeRate = feeRate;
                transactionTemplate.execute(status -> {
                    Boolean result = true;
                    try {
                        // 返回费用，则更新支付费率信息
                        Payment updatePayment = new Payment();
                        updatePayment.setId(paymentId);
                        updatePayment.setFeeRate(new BigDecimal(finalFeeRate));
                        // 乐观更新
                        paymentMapper.updateFeeByPrimaryKeyCas(updatePayment);

                        // 删除重试数据
                        huiFuPaymentRateRetryService.deleteByPaymentId(paymentId);
                    } catch (Exception e) {
                        log.error("支付单{}更新支付费率信息失败", paymentId);
                        status.setRollbackOnly();
                        result = false;
                    }
                    return result;
                });
            } catch (Exception e) {
                log.error("汇付支付费率重试任务开始失败,paymentId:{},e", paymentId, e);
                huiFuPaymentRateRetryService.increaseRetryNumById(primaryId);
            }
        }

        log.info("汇付支付费率重试任务结束------调度任务:{}，本次需要处理的数量:{}", context.getJobParameters(), huiFuPaymentRateRetries.size());
        return new ProcessResult(true);
    }

}
