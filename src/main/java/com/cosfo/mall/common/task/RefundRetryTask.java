package com.cosfo.mall.common.task;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.PayCodeEnum;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.factory.RefundStrategyFactory;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.mall.payment.template.RefundTemplate;
import com.cosfo.mall.payment.template.huifu.HuiFuRefundTemplate;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.ProviderException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-04-23
 * @Description:
 */
@Component
@Slf4j
public class RefundRetryTask extends XianMuJavaProcessorV2 {

    @Resource
    private RefundMapper refundMapper;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;

    @Lazy
    @Resource
    private RefundStrategyFactory refundStrategyFactory;
    @Lazy
    @Resource
    private PaymentService paymentService;
    @Lazy
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private TenantService tenantService;
    @Resource
    private RefundService refundService;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;


    private static final String START_BEFORE_HOUR = "startBeforeHour";

    private static final String MAX_RETRY_NUM = "maxRetryNum";

    private static final String WARNING_NUM = "warningNum";

    private static final String CONFIG_TENANT_ID = "configTenantId";

    private static final String LIMIT_SIZE = "limitSize";

    @Override
    public ProcessResult processResult(XmJobInput context) {
        log.info("退款重试定时任务开始------");

        Integer startBeforeHour = NumberConstant.TWENTY_FOUR;
        Integer maxRetryNum = NumberConstant.EIGHT;
        Integer warningNum = NumberConstant.TEN;
        Integer limitSize = NumberConstant.TEN;
        try {
            log.info("executeRetry jobParameters:{}", context.getJobParameters());

            JSONObject jsonObject = JSONObject.parseObject(context.getJobParameters());
            if (jsonObject.containsKey(START_BEFORE_HOUR)) {
                startBeforeHour = jsonObject.getInteger(START_BEFORE_HOUR);
            }
            if (jsonObject.containsKey(MAX_RETRY_NUM)) {
                maxRetryNum = jsonObject.getInteger(MAX_RETRY_NUM);
            }
            if (jsonObject.containsKey(WARNING_NUM)) {
                warningNum = jsonObject.getInteger(WARNING_NUM);
                if (warningNum == 0) {
                    warningNum = NumberConstant.TEN;
                }
            }
            if (jsonObject.containsKey(LIMIT_SIZE)) {
                limitSize = jsonObject.getInteger(LIMIT_SIZE);
            }
        } catch (Exception e) {
            log.error("executeRetry 转换失败 jobParameters:{}", context.getJobParameters());
        }

        // 近二十四小时内的数据进行轮询处理
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = now;
        LocalDateTime startTime = now.minusHours(startBeforeHour);

        // 查询需退款数据
        List<Integer> statusList = Lists.newArrayList(RefundEnum.Status.CREATE_REFUND.getStatus(), RefundEnum.Status.IN_REFUND.getStatus());
        List<Refund> refunds = refundMapper.selectByNeedRetryList(startTime, endTime, statusList, maxRetryNum, limitSize);
        log.info("退款重试定时任务扫描本次执行量{}条数据", refunds.size());
        executeRetry(refunds, warningNum);

        log.info("退款重试定时任务结束------");
        return new ProcessResult(true);
    }

    public void executeRetry(List<Refund> refunds, Integer warningNum) {
        for (Refund refund : refunds) {
            Exception exception = null;
            try {
                // 执行退款
                execute(refund);
            } catch (Exception e) {
                exception = e;
            }
            // 自增重试次数
            refundMapper.increaseRetryNumById(refund.getId());
            refundService.logRefundResult(refund, exception, warningNum);
        }
    }

    private void execute(Refund refund) throws InterruptedException {
        Pair<RefundTemplate, RefundExecuteRequest> refundTemplateRefundExecuteRequestPair = assemblyExecuteRequest(refund);
        RefundTemplate refundTemplate = refundTemplateRefundExecuteRequestPair.getKey();
        RefundExecuteRequest request = refundTemplateRefundExecuteRequestPair.getValue();
        refundTemplate.executeRefund(request);

        if (refundTemplate instanceof HuiFuRefundTemplate) {
            // 汇付防止并发调用,休眠一秒
            Thread.sleep(NumberConstant.ONE_THOUSAND);
        }
    }

    private Pair<RefundTemplate, RefundExecuteRequest> assemblyExecuteRequest(Refund refund) {
        RefundExecuteRequest request = new RefundExecuteRequest();
        request.setRefund(refund);

        Long paymentId = refund.getPaymentId();
        Payment payment = null;
        RefundTemplate refundTemplate = null;
        // 记录支付单id的场景
        if (paymentId != null) {
            payment = paymentService.queryById(paymentId);
            if (payment == null) {
                throw new ParamsException("未查询到支付单信息");
            }
            // 如果非组合支付 直接取 组合支付取非现结
            String tradeType = getTradeType(payment);
            refundTemplate = refundStrategyFactory.getTemplateByTradeType(tradeType, refund.getRefundPrice());
            request.setPayment(payment);
            return Pair.of(refundTemplate, request);
        }

        // 主要为了兼容账期、余额没有支付单的场景 只能通过订单找到对应的退款渠道
        OrderAfterSaleResp orderAfterSale = orderAfterSaleService.queryById(refund.getAfterSaleId());
        Long orderId = orderAfterSale.getOrderId();
        Long tenantId = orderAfterSale.getTenantId();
        PaymentItem item = paymentItemMapper.selectPaySuccessByOrderId(tenantId, orderId);
        if (item != null) {
            payment = paymentService.queryById(item.getPaymentId());
        }
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderAfterSale.getOrderId()));
        String refundCode = PayCodeEnum.getRefundCode(orderDTO.getPayType(), orderDTO.getOnlinePayChannel(), refund.getRefundPrice());
        request.setPayment(payment);
        refundTemplate = refundStrategyFactory.getRefundChannel(refundCode);
        return Pair.of(refundTemplate, request);
    }

    private String getTradeType(Payment payment) {
        if (!Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            return payment.getTradeType();
        }
        String paymentNo = payment.getPaymentNo();
        List<PaymentCombinedDetail> details = paymentCombinedDetailService.selectByCombinedPaymentNo(paymentNo);
        return details.stream()
                .filter(el -> Objects.isNull(el.getOnlinePayChannel()))
                .map(PaymentCombinedDetail::getTradeType)
                .findFirst()
                .orElseThrow(() -> new ProviderException("未查询到本地支付子单"));
    }
}
