//package com.cosfo.mall.common.task;
//
//import com.alibaba.schedulerx.worker.processor.ProcessResult;
//import com.cosfo.mall.product.service.CategoryService;
//import lombok.extern.slf4j.Slf4j;
//import net.xianmu.task.process.XianMuJavaProcessorV2;
//import net.xianmu.task.vo.input.XmJobInput;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.Collections;
//
///**
// * 描述: 鲜沐类目同步任务
// *
// * @author: Cathy
// */
//@Component
//@Slf4j
//public class CategorySynchronizedTask extends XianMuJavaProcessorV2 {
//    @Resource
//    private CategoryService categoryService;
//
//    @Override
//    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
//        log.info("同步分类------调度任务:{}", jobContext.getJobParameters());
//
//        categoryService.syncFromSummerForm(Collections.emptySet());
//
//        log.info("同步分类------调度任务:{}", jobContext.getJobParameters());
//        return new ProcessResult(true);
//    }
//}
