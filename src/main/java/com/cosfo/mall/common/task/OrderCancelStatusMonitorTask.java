package com.cosfo.mall.common.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.common.config.BusinessTimeConfig;
import com.cosfo.mall.common.constants.OrderCancelTypeEnum;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单取消状态监控告警
 * 定时拉取MySQL做30分钟过期监控和取消订单；
 *
 * @author: xiaowk
 * @date: 2023/11/10 上午11:52
 */
@Component
@Slf4j
public class OrderCancelStatusMonitorTask extends XianMuJavaProcessorV2 {


    @Resource
    private OrderService orderService;
    @Resource
    private BusinessTimeConfig businessTimeConfig;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;


    private static ConditionDTO defaultConditionDTO = new ConditionDTO(1, 5);

    /**
     * 查询可以超时取消的订单状态
     */
    private static final List<Integer> NEED_CANCEL_STATUS_LIST = Lists.newArrayList(OrderStatusEnum.CREATING_ORDER.getCode(), OrderStatusEnum.NO_PAYMENT.getCode());

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("订单取消状态监控告警 task start, 参数：{}", context);

        // 查询下单时间40分钟(配置的超时取消时间+5分钟)后仍然处于【1-下单中，2-待支付】状态订单，告警，并执行取消订单

        ConditionDTO conditionDTO = getConditionDTO(context);

        Long cancelMaxTimeout = businessTimeConfig.getOrderCancelTime() + businessTimeConfig.getOrderCancelTimeOut() + conditionDTO.getTimeoutMinutes();

        OrderQueryReq queryReq = new OrderQueryReq();
        queryReq.setStatusList(NEED_CANCEL_STATUS_LIST);
        queryReq.setCreateEndTime(LocalDateTime.now().minusMinutes(cancelMaxTimeout));
        queryReq.setCreateStartTime(LocalDate.now().atStartOfDay().minusDays(conditionDTO.getScanDays()));
        List<OrderResp> orderDTOList = RpcResultUtil.handle(orderQueryProvider.queryOrderList(queryReq));

        if (!CollectionUtils.isEmpty(orderDTOList)) {

            String errMsg = String.format("发现存在%s条订单%s分钟后仍然在【1-下单中、2-待支付】状态，即将执行取消操作，订单如下%s", orderDTOList.size(), cancelMaxTimeout, orderDTOList.stream().map(OrderResp::getOrderNo).collect(Collectors.toList()));
            log.error(errMsg, new BizException(errMsg));

            orderDTOList.forEach(e -> {
                try {
                    orderService.cancel(e.getId(), null, OrderCancelTypeEnum.TIME_OUT_CANCEL.getType());
                } catch (Exception ex) {
                    log.error("定时任务超时取消订单异常，orderNo={}", e.getOrderNo(), ex);
                }
            });
        }

        log.info("订单取消状态监控告警 task end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }

    private ConditionDTO getConditionDTO(XmJobInput context) {
        ConditionDTO conditionDTO = defaultConditionDTO;

        String jobParameters = context.getJobParameters();
        String instanceParameters = context.getInstanceParameters();

        if (!StringUtils.isEmpty(jobParameters)) {
            conditionDTO = JSONObject.parseObject(jobParameters, ConditionDTO.class);
        }
        if (!StringUtils.isEmpty(instanceParameters)) {
            conditionDTO = JSONObject.parseObject(instanceParameters, ConditionDTO.class);
        }

        if (conditionDTO == null) {
            throw new ProviderException("任务参数配置错误");
        }

        if (conditionDTO.getScanDays() == null) {
            conditionDTO.setScanDays(defaultConditionDTO.getScanDays());
        }
        if (conditionDTO.getTimeoutMinutes() == null) {
            conditionDTO.setTimeoutMinutes(defaultConditionDTO.getTimeoutMinutes());
        }

        return conditionDTO;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ConditionDTO implements Serializable {

        /**
         * 扫描订单记录天数 默认1天
         */
        private Integer scanDays;

        /**
         * 告警超时时间(分钟) 额外默认5分钟（正常超时时间35分钟）
         */
        private Integer timeoutMinutes;

    }
}
