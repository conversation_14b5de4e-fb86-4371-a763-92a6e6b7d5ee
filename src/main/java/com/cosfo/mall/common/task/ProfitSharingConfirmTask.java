package com.cosfo.mall.common.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2024-01-15
 **/
@Component
@Slf4j
public class ProfitSharingConfirmTask extends XianMuJavaProcessorV2 {

    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("分账确认定时任务开始");
        billProfitSharingOrderService.confirmProfitSharingTask();
        log.info("分账确认定时任务结束");
        return new ProcessResult(true);
    }
}
