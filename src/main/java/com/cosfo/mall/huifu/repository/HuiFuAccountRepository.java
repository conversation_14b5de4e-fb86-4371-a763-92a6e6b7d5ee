package com.cosfo.mall.huifu.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.huifu.model.po.HuiFuAccount;

import java.util.Collection;
import java.util.List;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-01-09
 **/
public interface HuiFuAccountRepository extends IService<HuiFuAccount> {

    /**
     * 根据账号ids查询
     *
     * @param accountIds
     * @return
     */
    List<HuiFuAccount> queryByAccountIds(Long tenantId, Collection<Long> accountIds, Integer accountType);
}
