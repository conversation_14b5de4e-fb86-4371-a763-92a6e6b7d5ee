package com.cosfo.mall.huifu.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.huifu.mapper.HuiFuAccountMapper;
import com.cosfo.mall.huifu.model.po.HuiFuAccount;
import com.cosfo.mall.huifu.repository.HuiFuAccountRepository;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-01-09
 **/
@Service
public class HuiFuAccountRepositoryImpl extends ServiceImpl<HuiFuAccountMapper, HuiFuAccount> implements HuiFuAccountRepository {

    @Override
    public List<HuiFuAccount> queryByAccountIds(Long tenantId, Collection<Long> accountIds, Integer accountType) {
        LambdaQueryWrapper<HuiFuAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HuiFuAccount::getAccountId, accountIds);
        queryWrapper.eq(HuiFuAccount::getAccountType, accountType);
        queryWrapper.eq(HuiFuAccount::getTenantId, tenantId);
        return list(queryWrapper);
    }
}
