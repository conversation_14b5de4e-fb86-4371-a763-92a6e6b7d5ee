package com.cosfo.mall.order.model.bo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-04-22
 **/
@Data
@Builder
public class OrderNonCashCalculationResultBO {
    // 可用非现金金额
    private BigDecimal usableNonCashBalance;
    // 不可用非现金金额
    private BigDecimal unusableNonCashBalance;
    // 订单总金额
    private BigDecimal totalOrderAmount;
    // 是否存在不可用项
    private boolean hasUnusableItems;
}
