package com.cosfo.mall.order.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.ProfitSharingRuleTypeEnum;
import com.cosfo.mall.common.context.DeliveryTypeEnum;
import com.cosfo.mall.common.context.TenantTypeEnum;
import com.cosfo.mall.common.utils.AssertCheckDefault;
import com.cosfo.mall.order.service.ProfitSharingCalculate;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Slf4j
@Service
public class ProprietaryProductProfitSharingCalculate implements ProfitSharingCalculate {
    @Resource
    private BillProfitSharingService billProfitSharingService;
    @Resource
    private TenantService tenantService;


    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;

    @Override
    public boolean support(Integer deliveryType, Integer profitSharingRuleType, Integer type) {
        return DeliveryTypeEnum.BRAND_DELIVERY.getCode().equals(deliveryType) && ProfitSharingRuleTypeEnum.PROPRIETARY_SKU.getCode().equals(profitSharingRuleType);
    }

    @Override
    public void profitSharingCalculate(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, Map<Long, BigDecimal> accountProfitSharingPriceMap) {
        // 查询订单信息
//        Order order = orderMapper.selectByPrimaryKey(billProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getOrderId());
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(billProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getOrderId()));
        // 查询订单项信息
//        List<OrderItem> orderItems = orderItemMapper.selectByOrderId(order.getTenantId(), order.getId());
        List<OrderItemAndSnapshotResp> orderItemList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderDTO.getId()));

        OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
        afterSaleQueryReq.setTenantId(orderDTO.getTenantId());
        afterSaleQueryReq.setOrderIds(Lists.newArrayList(orderDTO.getId()));
        afterSaleQueryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(afterSaleQueryReq));


        // 查询已经完成的售后单
//        List<OrderAfterSaleDTO> orderAfterSaleDTOS = afterSaleMapper.queryAllFinishedAfterSaleOrderByOrderId(order.getId(), order.getTenantId());

        // 计算商品金额
        BigDecimal productTotalPrice = BigDecimal.ZERO;
        Iterator<OrderItemAndSnapshotResp> iterator = orderItemList.iterator();
        while (iterator.hasNext()){
            OrderItemAndSnapshotResp orderItem = iterator.next();
            productTotalPrice = NumberUtil.add(productTotalPrice, orderItem.getTotalPrice());
        }

        // 计算售后退款金额
        if(!CollectionUtils.isEmpty(afterSaleDTOList)){
            for (OrderAfterSaleResp orderAfterSaleDto : afterSaleDTOList) {
                BigDecimal afterSaleProductPrice = NumberUtil.sub(orderAfterSaleDto.getTotalPrice(), Objects.isNull(orderAfterSaleDto.getDeliveryFee()) ? BigDecimal.ZERO : orderAfterSaleDto.getDeliveryFee());
                productTotalPrice = NumberUtil.sub(productTotalPrice, afterSaleProductPrice);
            }
        }

//        TenantDTO tenantDTO = tenantService.selectByType(TenantTypeEnum.FANTAI.getType());
        BillProfitSharingSnapshotDTO fantaiBillProfitSharingSnapShotDto = null;
        BigDecimal residuePrice = productTotalPrice;
        Long tenantId = orderDTO.getTenantId();
        for(BillProfitSharingSnapshotDTO billProfitSharingSnapshotDto:billProfitSharingSnapshotDtos){
            if (!tenantId.equals(billProfitSharingSnapshotDto.getAccountId())) {
                billProfitSharingSnapshotDto.setOriginPrice(productTotalPrice);
                BigDecimal profitSharingPrice = NumberUtil.mul(productTotalPrice, NumberUtil.div(billProfitSharingSnapshotDto.getNumber(), NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
                residuePrice = NumberUtil.sub(residuePrice, profitSharingPrice);
                billProfitSharingSnapshotDto.setProfitSharingPrice(profitSharingPrice);
                billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDto);
            }else {
                fantaiBillProfitSharingSnapShotDto = billProfitSharingSnapshotDto;
            }
        }
//        AssertCheckDefault.expectNotNull(fantaiBillProfitSharingSnapShotDto, "帆台分账明细不存在:" + JSON.toJSONString(billProfitSharingSnapshotDtos));
        // 帆台
        fantaiBillProfitSharingSnapShotDto.setOriginPrice(productTotalPrice);
        fantaiBillProfitSharingSnapShotDto.setProfitSharingPrice(residuePrice);
        billProfitSharingService.updateBillProfitSharingSnapshot(fantaiBillProfitSharingSnapShotDto);
    }
}
