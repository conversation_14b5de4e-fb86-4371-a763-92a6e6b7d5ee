package com.cosfo.mall.tenant.service;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.model.po.Tenant;
import com.cosfo.mall.tenant.model.vo.TenantCustomerPhoneVO;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/18
 */
public interface TenantService {
    /**
     * 查询供应商信息
     *
     * @param supplierTenantIds
     * @return
     */
    List<Tenant> querySupplierInfoBySupplierTenantIds(List<Long> supplierTenantIds);

    /**
     * 查询用户小程序信息
     * @param tenantId 租户id
     * @return 小程序认证信息
     */
    TenantAuthConnectionDTO queryTenantAuthConnection(Long tenantId);

    /**
     * 使用appId查询小程序信息
     * @param appId appId
     * @return 小程序信息
     */
    TenantAuthConnectionDTO queryByAppId(String appId);

    /**
     * 查询租户信息
     * @param tenantId
     * @return
     */
    ResultDTO<TenantDTO> selectTenantInfo(Long tenantId);

    /**
     * 根据主键查询
     * @param tenantId
     * @return
     */
    Tenant selectByPrimaryKey(Long tenantId);

    /**
     * 根据类型查询租户信息
     *
     * @param type
     * @return
     */
    TenantDTO selectByType(Integer type);

    /**
     * 查询品牌方客服电话
     * @param tenantId
     * @return
     */
    List<TenantCustomerPhoneVO> queryTenantCustomerPhone(Long tenantId);

    boolean getSaveWorrySwitchByTenantId(Long tenantId);

    /**
     * 查询是否使用手机号验证码
     *
     * @param id
     * @return
     */
    Boolean queryUsePhoneVerificationFlag(Long id);

    /**
     * 查询限制多仓标识
     *
     * @param tenantId
     * @return
     */
    Boolean queryLimitMultiWarehouse(Long tenantId);
}
