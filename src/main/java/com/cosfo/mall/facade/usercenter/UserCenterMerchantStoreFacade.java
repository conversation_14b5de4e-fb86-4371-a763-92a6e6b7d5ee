package com.cosfo.mall.facade.usercenter;

import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.common.context.MerchantStoreStatusEnum;
import com.cosfo.mall.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.regional.provider.RegionalOrganizationQueryProvider;
import net.xianmu.usercenter.client.regional.resp.RegionalOrganizationResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterMerchantStoreFacade {

    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;
    @DubboReference
    private MerchantStoreCommandProvider merchantStoreCommandProvider;
    @DubboReference
    private RegionalOrganizationQueryProvider regionalOrganizationQueryProvider;

    /**
     * 获取门店信息
     *
     * @param id
     * @return
     */
    public MerchantStoreResultResp getMerchantStoreById(Long id) {
        DubboResponse<MerchantStoreResultResp> response = merchantStoreQueryProvider.getMerchantStoreById(id);
        if (!response.isSuccess()) {
            throw new BizException("获取门店信息失败");
        }
        return response.getData();
    }

    /**
     * 获取门店列表
     *
     * @param ids
     * @return
     */
    public List<MerchantStoreResultResp> getMerchantStoreList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        DubboResponse<List<MerchantStoreResultResp>> response = merchantStoreQueryProvider.getMerchantStoreByIds(ids);
        if (!response.isSuccess()) {
            throw new BizException("获取获取门店列表失败");
        }
        return response.getData();
    }


    /**
     * 根据租户和门店编号获取门店信息
     * @param tenantId
     * @param storeNo 门店编号（自定义外部编号）
     * @return
     */
    public MerchantStoreResultResp getMerchantStoreByStoreNo(Long tenantId, String storeNo) {
        if (tenantId == null || org.apache.commons.lang3.StringUtils.isBlank(storeNo)) {
            return null;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setTenantId(tenantId);
        req.setStatus(MerchantStoreStatusEnum.AUDIT_SUCCESS.getStatus());
        req.setStoreNo(storeNo);

        DubboResponse<List<MerchantStoreResultResp>> response = merchantStoreQueryProvider.getMerchantStores(req);
        if (!response.isSuccess()) {
            throw new ProviderException("获取门店信息失败，storeNo=" + storeNo);
        }

        List<MerchantStoreResultResp> resultList = response.getData();
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }

        if (resultList.size() > 1) {
            log.error("门店信息不唯一，tenantId={}, storeNo={}", tenantId, storeNo, new BizException("门店信息不唯一"));
        }

        return resultList.get(0);
    }

    /**
     * 创建门店信息
     *
     * @param commandReq
     * @return
     */
    public Long createMerchantStoreInfo(MerchantStoreDomainCommandReq commandReq) {
        DubboResponse<Long> response = merchantStoreCommandProvider.createMerchantStoreInfo(SystemOriginEnum.COSFO_MALL, commandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response, "创建门店信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 更新门店（包含门店、账户、地址、联系人、分组）
     *
     * @param commandReq
     * @return
     */
    public Boolean updateMerchantStoreInfo(MerchantStoreDomainCommandReq commandReq) {
        DubboResponse<Boolean> response = merchantStoreCommandProvider.updateMerchantStoreInfo(SystemOriginEnum.COSFO_MALL, commandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"更新门店信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 获取租户默认区域组织
     * @param tenantId
     * @return
     */
    public RegionalOrganizationResultResp saasGetRegionalByTenantId(Long tenantId) {
        DubboResponse<RegionalOrganizationResultResp> response = regionalOrganizationQueryProvider.saasGetRegionalByTenantId(tenantId);
        if (!response.isSuccess()) {
            throw new BizException("获取租户默认区域组织失败");
        }
        return response.getData();
    }


    /**
     * 查询门店名称
     * @param storeId
     * @return
     */
    public String queryStoreName(Long storeId) {
        if(storeId == null){
            return null;
        }
        try {
            MerchantStoreResultResp storeResultResp = RpcResponseUtil.handler(merchantStoreQueryProvider.getMerchantStoreById(storeId));
            if(storeResultResp != null){
                return storeResultResp.getStoreName();
            }
        } catch (Exception e) {
            log.error("查询门店信息异常，storeId={}", storeId, e);
        }
        return null;
    }
}
