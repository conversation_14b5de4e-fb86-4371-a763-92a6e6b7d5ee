package com.cosfo.mall.stock.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.constant.MarketConstant;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.enums.StockRecordType;
import com.cofso.item.client.resp.StockResp;
import com.cofso.preferential.client.req.OptAvailableQuantityReq;
import com.cofso.preferential.client.resp.ProductSkuCityPreferentialCostPriceResp;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.common.constant.MqTagConstant;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.constants.OrderEnums.WarehouseTypeEnum;
import com.cosfo.mall.common.constants.PresaleSwitchEnum;
import com.cosfo.mall.facade.*;
import com.cosfo.mall.facade.converter.ProductSkuPreferentialCostPriceFacadeConvert;
import com.cosfo.mall.facade.converter.SaleInventoryConvert;
import com.cosfo.mall.facade.dto.DeliveryDate4SkuDTO;
import com.cosfo.mall.facade.dto.DeliveryDateSkuQueryDTO;
import com.cosfo.mall.facade.dto.WarehouseInventoryDTO;
import com.cosfo.mall.facade.dto.WarehouseInventoryQueryDTO;
import com.cosfo.mall.facade.ofc.OfcDeliveryInfoFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.mall.market.model.dto.MarketItemDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.order.model.dto.OrderDTO;
import com.cosfo.mall.order.model.dto.OrderItemDTO;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.product.model.dto.ProductPricingSupplyDTO;
import com.cosfo.mall.stock.model.dto.OrderSelfSupplyOccupyDTO;
import com.cosfo.mall.stock.model.dto.PreDistributionOrderItemDTO;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.cosfo.mall.stock.model.dto.StockQueryDTO;
import com.cosfo.mall.stock.service.StockService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderStatusUpdateReq;
import com.cosfo.ordercenter.client.req.event.LockStockSuccessReq;
import com.cosfo.ordercenter.client.req.event.OrderCancelReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.xianmu.inventory.client.saleinventory.dto.req.*;
import net.xianmu.inventory.client.saleinventory.dto.res.PreDistributionNotOccupySkuDetailResDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.PreDistributionOrderOccupyResDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.PreDistributionSkuDetailResDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.TryMostOccupableDetail;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/17  15:04
 */
@Slf4j
@Service
public class StockServiceImpl implements StockService {
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private WarehouseInventoryFacade warehouseInventoryFacade;
    @Resource
    private SaleInventoryCommandFacade saleInventoryCommandFacade;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private StockFacade stockFacade;
    @Resource
    private ProductQueryFacade productQueryFacade;
    @Lazy
    @Resource
    private OrderService orderService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private ProductSkuPreferentialCostPriceFacade productSkuPreferentialCostPriceFacade;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private OfcDeliveryInfoFacade ofcDeliveryInfoFacade;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderCommandProvider orderCommandProvider;

    /**
     * 帆台支付账号
     */
    @Value(value = "${tenant.sugeTenantId}")
    private Long sugeTenantId;

    @Override
    public Map<Long, StockDTO> queryStockAmount(LoginContextInfoDTO context, MerchantAddressDTO orderAddress, List<Long> itemIds, Boolean isCombineSub) {
        Map<Long, StockDTO> stockResult = new HashMap<>();

        Map<Long, ProductPricingSupplyDTO> supplySkuMap = new HashMap<>();
        Integer onSale = null;
        if (isCombineSub != null && isCombineSub){
            onSale = MarketConstant.NO_SALE_CODE;
        }

        List<MarketItemDTO> marketItemList = marketItemService.selectSimpleItemByItemIds(context.getTenantId(),context.getStoreId(),itemIds, onSale);

        // 无货商品 itemId
        Set<Long> noGoodItemIds = marketItemList.stream().filter(marketItem -> GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(marketItem.getGoodsType())).map(MarketItemDTO::getId).collect(Collectors.toSet());

        // 自营货品 skuId
        Set<Long> selfGoodSkuIds = marketItemList.stream().filter(marketItem -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItem.getGoodsType())).map(MarketItemDTO::getSkuId).collect(Collectors.toSet());

        // 报价货品 skuId
        Set<Long> quotationSkuIds = marketItemList.stream().filter(marketItem -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItem.getGoodsType())).map(MarketItemDTO::getSkuId).collect(Collectors.toSet());


        // 无货商品库存
        Map<Long, StockResp> noGoodStockMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(noGoodItemIds)) {
            List<StockResp> stockResps = stockFacade.batchQuery(context.getTenantId(), new ArrayList<>(noGoodItemIds));
            noGoodStockMap = stockResps.stream().collect(Collectors.toMap(StockResp::getItemId, Function.identity(), (k1, k2) -> k1));
        }

        // 自营或报价货品组装参数
        List<WarehouseInventoryQueryDTO.SkuDetail> allSkuDetailList = new ArrayList<>();

        // 自营货品
        if (CollectionUtil.isNotEmpty(selfGoodSkuIds)) {

            List<ProductSkuDetailResp> productSkuDetailResps = productQueryFacade.querySkuInfo(new ArrayList<>(selfGoodSkuIds));

            allSkuDetailList.addAll(productSkuDetailResps.stream().map(e -> {
                WarehouseInventoryQueryDTO.SkuDetail skuDetail = new WarehouseInventoryQueryDTO.SkuDetail();
                skuDetail.setSkuCode(e.getSku());
                skuDetail.setSkuId(e.getSkuId());
                // 自营skuTenantId是品牌方
                skuDetail.setSkuTenantId(context.getTenantId());
                skuDetail.setCategoryType(e.getCategoryType());
                return skuDetail;
            }).collect(Collectors.toList()));
        }

        // 报价货品
        if (CollectionUtil.isNotEmpty(quotationSkuIds)) {
            List<ProductSkuDetailResp> productSkuDetailResps = productQueryFacade.querySkuInfo(new ArrayList<>(quotationSkuIds));

            allSkuDetailList.addAll(productSkuDetailResps.stream().map(e -> {
                WarehouseInventoryQueryDTO.SkuDetail skuDetail = new WarehouseInventoryQueryDTO.SkuDetail();
                skuDetail.setSkuCode(e.getSku());
                skuDetail.setSkuId(e.getSkuId());
                // 报价skuTenantId是鲜沐1
                skuDetail.setSkuTenantId(XianmuSupplyTenant.TENANT_ID);
                skuDetail.setCategoryType(e.getCategoryType());
                return skuDetail;
            }).collect(Collectors.toList()));
        }

        for (MarketItemDTO marketItem : marketItemList) {
            // 无货商品 -> 虚拟商品
            if (GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(marketItem.getGoodsType())) {
                StockResp stockResp = noGoodStockMap.get(marketItem.getId());
                if (Objects.nonNull(stockResp)){
                    StockDTO dto = new StockDTO();
                    dto.setId(stockResp.getId());
                    dto.setTenantId(stockResp.getTenantId());
                    dto.setItemId(stockResp.getItemId());
                    dto.setAmount(stockResp.getAmount());
                    dto.setSupplyTenantId(stockResp.getTenantId());
                    stockResult.put(marketItem.getId(), dto);
                }
            }
        }

        if (!CollectionUtils.isEmpty(allSkuDetailList)) {
            try {
                // 按照skuId分组marketItem
                Map<Long, List<MarketItemDTO>> skuId2ItemListMap = marketItemList.stream()
                        .filter(e -> (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(e.getGoodsType()) || GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(e.getGoodsType())) && e.getSkuId() != null)
                        .collect(Collectors.groupingBy(MarketItemDTO::getSkuId));

                Map<Long, MarketItemDTO> itemId2MarketItemMap = marketItemList.stream().collect(Collectors.toMap(MarketItemDTO::getId, Function.identity(), (v1, v2) -> v1));

                Map<Long, StockDTO> stockMap = queryInventoryCenterStock(orderAddress, allSkuDetailList, skuId2ItemListMap, itemId2MarketItemMap);

                stockResult.putAll(stockMap);
            } catch (Exception e) {
                log.error("获取库存数量失败，allSkuDetailList={}", JSON.toJSONString(allSkuDetailList), e);
            }
        }

        return stockResult;
    }


    @Override
    public void lockStock(LoginContextInfoDTO context, OrderDTO orderDto) {
        //扣除无仓库存
        if (OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(orderDto.getWarehouseType())) {
            // TODO 按照itemId排序
            List<OrderItemDTO> orderItemDTOList = orderDto.getOrderItemDTOList();
            orderItemDTOList.stream().sorted(Comparator.comparing(OrderItemDTO::getItemId));
            for (OrderItemDTO itemDTO : orderItemDTOList) {
                decreaseSelfStock(context.getTenantId(), StockRecordType.ORDER, itemDTO.getItemId(), itemDTO.getAmount(), orderDto.getOrderNo());
            }
            // 冻结自营仓库存
        } else if (OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(orderDto.getWarehouseType())) {
            // 发送消息，异步处理
            OrderSelfSupplyOccupyDTO orderSelfSupplyOccupyDTO = new OrderSelfSupplyOccupyDTO();
            orderSelfSupplyOccupyDTO.setOrderNo(orderDto.getOrderNo());
            orderSelfSupplyOccupyDTO.setTenantId(orderDto.getTenantId());
            log.info("发送处理自营仓库存冻结消息：{}", JSONObject.toJSONString(orderSelfSupplyOccupyDTO));
            //发送顺序消息
            mqProducer.send(MQTopicConstant.SELF_SUPPLY_ORDER_OCCUPY, MqTagConstant.TAG_SELF_SUPPLY_ORDER_OCCUPY, orderSelfSupplyOccupyDTO);

        } else if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDto.getWarehouseType())) {
            //处理三方仓库存
            boolean lockStockSuccess = false;
            try {
                OrderOccupyReqDTO orderOccupyReqDTO = SaleInventoryConvert.convert2OrderOccupyReqDTO(orderDto);
                orderOccupyReqDTO.setOperatorName(userCenterMerchantStoreFacade.queryStoreName(orderDto.getStoreId()));
                saleInventoryCommandFacade.orderOccupy(orderOccupyReqDTO);
                lockStockSuccess = true;
            } catch (Exception e) {
                log.error("三方仓冻结库存失败：orderNo={}", orderDto.getOrderNo(), e);
            }

            //省心定库存
            boolean lockPreStockSuccess = false;
            try {
                List<OptAvailableQuantityReq> optAvailableQuantityReqs = ProductSkuPreferentialCostPriceFacadeConvert.INSTANCE.convert2OptAvailableQuantityReqList (orderDto.getOrderItemDTOList ());
                productSkuPreferentialCostPriceFacade.occupyAvailableQuantity(context.getTenantId (), orderDto.getOrderAddress ().getCity (),orderDto.getId (),optAvailableQuantityReqs);
                lockPreStockSuccess = true;
            } catch (Exception e) {
                log.error("省心定冻结库存失败：orderNo={}", orderDto.getOrderNo(), e);
            }

            // 冻结库存失败，或者更新订单状态失败，释放库存，取消订单
            if(!lockStockSuccess || !lockPreStockSuccess || !updateOrderAfterLockStockSuccess(orderDto)){
                cancelOrderAfterLockStockFail(orderDto);
            }
        }
    }

    /**
     * 三方仓冻结库存成功后，更新订单状态为待支付
     *
     * @param orderDto
     * @return
     */
    private boolean updateOrderAfterLockStockSuccess(OrderDTO orderDto) {
        try {
            return Boolean.TRUE.equals(transactionTemplate.execute(status -> {
                // 保存供应商运费
                orderService.saveSupplierDeliveryFee(orderDto);
//                // 更新订单状态为待支付
//                orderMapper.updateOrderStatusById(orderDto.getId(), OrderStatusEnum.CREATING_ORDER.getCode(), OrderStatusEnum.NO_PAYMENT.getCode(), orderDto.getDeliveryTime());
//                orderItemMapper.batchUpdateOrderItemStatus(orderDto.getTenantId(), orderDto.getId(), OrderItemStatusEnum.NO_PAYMENT.getCode());
//
                LockStockSuccessReq lockStockSuccessReq = new LockStockSuccessReq();
                lockStockSuccessReq.setOrderId(orderDto.getId());
                lockStockSuccessReq.setTenantId(orderDto.getTenantId());
                RpcResultUtil.handle(orderCommandProvider.lockStockSuccess(lockStockSuccessReq));
                return Boolean.TRUE;
            }));

        } catch (Exception e) {
            log.error("三方仓冻结库存成功后，更新订单状态失败，orderNo={}", orderDto.getOrderNo(), e);
            return false;
        }
    }

    /**
     * 冻结库存失败，或者更新订单状态失败，释放库存，取消订单
     *
     * @param orderDto
     * @return
     */
    public void cancelOrderAfterLockStockFail(OrderDTO orderDto) {
        try {
            unlockStockByCancel(orderDto.getTenantId(), orderDto);
        } catch (Exception e) {
            log.error("三方仓释放库存失败, orderNo={}", orderDto.getOrderNo(), e);
        }
        try {
            OrderCancelReq orderCancelReq = new OrderCancelReq();
            orderCancelReq.setOrderIds(Lists.newArrayList(orderDto.getId()));
            orderCancelReq.setTenantId(orderDto.getTenantId());
            orderCancelReq.setTimeoutCancel(false);
//            Lists.newArrayList(orderDto.getId()), orderDto.getTenantId(), false)
            RpcResultUtil.handle(orderCommandProvider.cancel(orderCancelReq));
        } catch (Exception e) {
            log.error("三方仓取消订单失败, orderNo={}", orderDto.getOrderNo(), e);
        }
    }

    @Override
    public void unlockStockByCancel(Long tenantId, OrderDTO orderDto) {
        WarehouseTypeEnum warehouseTypeEnum = WarehouseTypeEnum.getByCode(orderDto.getWarehouseType());

        //处理无仓订单库存释放
        if (WarehouseTypeEnum.PROPRIETARY == warehouseTypeEnum) {
            for (OrderItemDTO itemDTO : orderDto.getOrderItemDTOList()) {
                increaseSelfStock(tenantId, StockRecordType.ORDER_CANCEL, itemDTO.getItemId(), itemDTO.getAmount(), orderDto.getOrderNo());
            }

        } else if (WarehouseTypeEnum.SELF_SUPPLY == warehouseTypeEnum) {
            // 处理自营仓订单库存释放

            // 查询代仓映射关系
            List<OrderItemDTO> orderItemDTOList = orderDto.getOrderItemDTOList();
            List<Long> skuIds = orderItemDTOList.stream().map(OrderItemDTO::getSkuId).collect(Collectors.toList());
            List<ProductAgentSkuDTO> productAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(skuIds, orderDto.getTenantId());
            Map<Long, ProductAgentSkuDTO> productAgentSkuDTOMap = productAgentSkuDTOS.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
            orderItemDTOList.forEach(orderItemDTO -> {
                ProductAgentSkuDTO productAgentSkuDTO = productAgentSkuDTOMap.get(orderItemDTO.getSkuId());
                orderItemDTO.setSupplySku(productAgentSkuDTO.getAgentSkuCode());
            });

            OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTO = SaleInventoryConvert.convertTOOrderReleaseBySpecifySkuReqDTO(orderDto);
            orderReleaseBySpecifySkuReqDTO.setOperatorName(userCenterMerchantStoreFacade.queryStoreName(orderDto.getStoreId()));
            saleInventoryCommandFacade.orderReleaseBySpecifySku(orderReleaseBySpecifySkuReqDTO);

        } else if (WarehouseTypeEnum.THREE_PARTIES == warehouseTypeEnum) {
            //处理三方仓订单库存释放
            log.info("新链路释放库存 orderDto={}", JSON.toJSONString(orderDto));

            // 三方仓 调用wms释放库存接口
            List<OrderItemDTO> orderItemDTOList = orderDto.getOrderItemDTOList();
            // 报价货品
            List<Long> quotationSkuIds = orderItemDTOList.stream().filter(e -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(e.getGoodsType())).map(OrderItemDTO::getSkuId)
                    .collect(Collectors.toList());
            List<ProductAgentSkuDTO> quotationProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(quotationSkuIds, XianmuSupplyTenant.TENANT_ID);

            // 代仓自营货品
            List<Long> selfGoodSkuIds = orderItemDTOList.stream().filter(e -> !GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(e.getGoodsType())).map(OrderItemDTO::getSkuId)
                    .collect(Collectors.toList());
            List<ProductAgentSkuDTO> selfGoodProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(selfGoodSkuIds, orderDto.getTenantId());
            List<ProductAgentSkuDTO> productAgentSkuDTOS = Lists.newArrayList(quotationProductAgentSkuDTOS);
            productAgentSkuDTOS.addAll(selfGoodProductAgentSkuDTOS);
            Map<Long, ProductAgentSkuDTO> productAgentSkuDTOMap = productAgentSkuDTOS.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
            orderItemDTOList.forEach(orderItemDTO -> {
                ProductAgentSkuDTO productAgentSkuDTO = productAgentSkuDTOMap.get(orderItemDTO.getSkuId());
                orderItemDTO.setSupplySku(productAgentSkuDTO.getAgentSkuCode());
            });

            OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTO = SaleInventoryConvert.convert2ReqDTOWithoutWarehouseNo(orderDto);
            orderReleaseBySpecifySkuReqDTO.setOperatorName(userCenterMerchantStoreFacade.queryStoreName(orderDto.getStoreId()));
            saleInventoryCommandFacade.orderReleaseBySpecifySku(orderReleaseBySpecifySkuReqDTO);

            List<OptAvailableQuantityReq> optAvailableQuantityReqs = ProductSkuPreferentialCostPriceFacadeConvert.INSTANCE.convert2OptAvailableQuantityReqList (orderItemDTOList.stream().filter(e -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(e.getGoodsType())).collect(Collectors.toList()));
            productSkuPreferentialCostPriceFacade.releaseAvailableQuantity (tenantId,orderDto.getId (),optAvailableQuantityReqs);
        }
    }


    @Override
    public void unlockStockByAfterSale(OrderAfterSaleResp saleDTO) {
        Long tenantId = saleDTO.getTenantId();
        Long storeId = saleDTO.getStoreId();
        //订单信息
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(saleDTO.getOrderId()));
        OrderItemAndSnapshotResp orderItemAndSnapshotDTO = RpcResultUtil.handle(orderItemQueryProvider.queryDetailById(saleDTO.getOrderItemId()));
        // 无仓订单
        if (OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(orderDTO.getWarehouseType())) {
            decreaseSelfStock(tenantId, StockRecordType.AFTER_SALE, orderItemAndSnapshotDTO.getItemId(), -saleDTO.getAmount(), saleDTO.getAfterSaleOrderNo());
        } else if(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType())) {
            // 处理三方仓
            String skucode = null;
            if(GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(orderItemAndSnapshotDTO.getGoodsType())){
                // 报价货品
                List<ProductAgentSkuDTO> quotationProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(Collections.singletonList(orderItemAndSnapshotDTO.getSkuId()), XianmuSupplyTenant.TENANT_ID);
                if(!CollectionUtils.isEmpty(quotationProductAgentSkuDTOS)){
                    skucode = quotationProductAgentSkuDTOS.get(0).getAgentSkuCode();
                }

            } else{
                List<ProductAgentSkuDTO> selfGoodProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(Collections.singletonList(orderItemAndSnapshotDTO.getSkuId()), orderDTO.getTenantId());
                if(!CollectionUtils.isEmpty(selfGoodProductAgentSkuDTOS)){
                    skucode = selfGoodProductAgentSkuDTOS.get(0).getAgentSkuCode();
                }
            }

            OrderReleaseBySpecifySkuReqDTO dto = new OrderReleaseBySpecifySkuReqDTO();
            dto.setTenantId(tenantId);
            dto.setOrderNo(orderDTO.getOrderNo ());
            dto.setOrderType(SaleStockChangeTypeEnum.SAAS_AFTER_SALE.getTypeName());
            dto.setOperatorNo(saleDTO.getAfterSaleOrderNo());
            dto.setIdempotentNo(saleDTO.getAfterSaleOrderNo());
            dto.setOperatorName(userCenterMerchantStoreFacade.queryStoreName(storeId));

            OrderReleaseSkuDetailReqDTO reqDTO = new OrderReleaseSkuDetailReqDTO ();
            // 三方仓不指定仓库号
//            reqDTO.setWarehouseNo(null);
            reqDTO.setSkuCode (skucode);
            reqDTO.setReleaseQuantity(orderItemAndSnapshotDTO.getAmount());
            dto.setOrderReleaseSkuDetailReqDTOS(Collections.singletonList(reqDTO));

            OptAvailableQuantityReq req = new OptAvailableQuantityReq();
            req.setSkuId (orderItemAndSnapshotDTO.getSkuId ());
            req.setOptQuantity (saleDTO.getAmount ());
            productSkuPreferentialCostPriceFacade.releaseAvailableQuantity (tenantId,orderDTO.getId (),Collections.singletonList (req));

            saleInventoryCommandFacade.orderReleaseBySpecifySku(dto);

        } else if(OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(orderDTO.getWarehouseType())) {
            // 处理自营仓
            String skucode = null;
            List<ProductAgentSkuDTO> selfGoodProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(Collections.singletonList(orderItemAndSnapshotDTO.getSkuId()), orderDTO.getTenantId());
            if(!CollectionUtils.isEmpty(selfGoodProductAgentSkuDTOS)){
                skucode = selfGoodProductAgentSkuDTOS.get(0).getAgentSkuCode();
            }

            OrderReleaseBySpecifySkuReqDTO dto = new OrderReleaseBySpecifySkuReqDTO();
            dto.setTenantId(tenantId);
            dto.setOrderNo(orderDTO.getOrderNo ());
            dto.setOrderType(SaleStockChangeTypeEnum.SAAS_AFTER_SALE.getTypeName());
            dto.setOperatorNo(saleDTO.getAfterSaleOrderNo());
            dto.setIdempotentNo(saleDTO.getAfterSaleOrderNo());
            dto.setOperatorName(userCenterMerchantStoreFacade.queryStoreName(storeId));

            OrderReleaseSkuDetailReqDTO reqDTO = new OrderReleaseSkuDetailReqDTO ();
            // 自营仓指定仓库号
            reqDTO.setWarehouseNo(Long.parseLong(orderDTO.getWarehouseNo()));
            reqDTO.setSkuCode(skucode);
            reqDTO.setReleaseQuantity(saleDTO.getAmount());
            dto.setOrderReleaseSkuDetailReqDTOS(Collections.singletonList(reqDTO));

            saleInventoryCommandFacade.orderReleaseBySpecifySku(dto);
        }

    }

    @Override
    public void increaseSelfStock(Long tenantId, StockRecordType recordType, Long itemId, Integer addAmount, String recordNo) {
        stockFacade.increaseSelfStock(tenantId, recordType, itemId, addAmount, recordNo);
    }

    @Override
    public void decreaseSelfStock(Long tenantId, StockRecordType recordType, Long skuId, Integer reduceAmount, String recordNo) {
        increaseSelfStock(tenantId, recordType, skuId, -reduceAmount, recordNo);
    }

    /**
     * 查询库存调度中心货品的库存
     * @param orderAddress
     * @param allSkuDetailList
     * @param skuId2ItemListMap
     * @return
     */
    private Map<Long, StockDTO> queryInventoryCenterStock(MerchantAddressDTO orderAddress, List<WarehouseInventoryQueryDTO.SkuDetail> allSkuDetailList, Map<Long, List<MarketItemDTO>> skuId2ItemListMap, Map<Long, MarketItemDTO> itemId2MarketItemMap) {
        if(CollectionUtils.isEmpty(allSkuDetailList)){
            return Collections.emptyMap();
        }

        allSkuDetailList.forEach(e -> {
            Long skuId = e.getSkuId();
            List<MarketItemDTO> marketItemDTOS = skuId2ItemListMap.getOrDefault(skuId, Collections.emptyList());
            boolean flag = marketItemDTOS.stream().allMatch(item -> PresaleSwitchEnum.PRESALE_ITEM.getCode().equals(itemId2MarketItemMap.getOrDefault(item.getId(), new MarketItemDTO()).getPresaleSwitch()));
            e.setPresaleSwitch(flag ? PresaleSwitchEnum.PRESALE_ITEM.getCode() : PresaleSwitchEnum.NORMAL_ITEM.getCode());
        });

        // 返回信息 itemId -> 库存信息
        Map<Long, StockDTO> stockResult = new HashMap<>();

        List<WarehouseInventoryDTO> inventoryDTOList = new ArrayList<>();
        List<List<WarehouseInventoryQueryDTO.SkuDetail>> partition = Lists.partition(allSkuDetailList, 50);
        for (List<WarehouseInventoryQueryDTO.SkuDetail> skuDetails : partition) {
            // 请求参数
            WarehouseInventoryQueryDTO stockInput = new WarehouseInventoryQueryDTO();
            stockInput.setProvince(orderAddress.getProvince());
            stockInput.setCity(orderAddress.getCity());
            stockInput.setArea(orderAddress.getArea());
            stockInput.setPoi(orderAddress.getPoiNote());
            stockInput.setTenantId(orderAddress.getTenantId());
            stockInput.setAddress(orderAddress.getAddress());
            stockInput.setStoreId(orderAddress.getStoreId());
            try {

                stockInput.setSkuDetailList(skuDetails);
                List<WarehouseInventoryDTO> dtoList = warehouseInventoryFacade.queryAreaStoreQuantityList(stockInput);
                inventoryDTOList.addAll(dtoList);
            } catch (Exception e) {
                log.error("鲜沐服务异常, 获取鲜沐库存异常, 请求：{}, 异常信息：{}", stockInput, e.getMessage(), e);
            }
        }

        // 省心订库存
        Set<Long> skuIds = allSkuDetailList.stream().map(WarehouseInventoryQueryDTO.SkuDetail::getSkuId).collect (Collectors.toSet ());
        Map<Long, ProductSkuCityPreferentialCostPriceResp> costPriceRespMap = productSkuPreferentialCostPriceFacade.queryPreferentialCostPriceBySkuIds4City (orderAddress.getTenantId (), orderAddress.getCityId (), skuIds);


        for (WarehouseInventoryDTO dto : inventoryDTOList) {
            // 一个skuId 可能对应多个itemId
            List<MarketItemDTO> marketItemDTOS = skuId2ItemListMap.get(dto.getSkuId());

            marketItemDTOS.stream().forEach(item -> {
                Long itemId = item.getId();
                ProductSkuCityPreferentialCostPriceResp costPriceResp = costPriceRespMap.get (item.getSkuId ());
                StockDTO resDto = new StockDTO();
                resDto.setItemId(itemId);
                resDto.setAmount (dto.getInventory ());
                if(ObjectUtil.isNotNull (costPriceResp)  && ObjectUtil.isNotNull (costPriceResp.getAvailableQuantity ())){
                    if(costPriceResp.getAvailableQuantity () < dto.getInventory ()){
                        resDto.setAmount (costPriceResp.getAvailableQuantity ());
                    }
                }
                if(dto.getFruitFlag() != null) {
                    resDto.setQuantityDate(dto.getFruitFlag() ? null : dto.getQualityDate()); //NOSONAR
                }
                //sku归属信息
                resDto.setTenantId(item.getTenantId());
                // TODO 不需要SupplySkuId
//                resDto.setSupplySkuId(item.getSupplySkuId());
                resDto.setSupplyTenantId(XianmuSupplyTenant.TENANT_ID);
                stockResult.put(itemId, resDto);
            });
        }

        return stockResult;
    }

    @Override
    public Map<Long, StockDTO> preDistributionOrderOccupy(StockQueryDTO stockQueryDTO) {
        Map<Long, StockDTO> itemId2StockDTOMap = preDistributionOrderOccupy(stockQueryDTO, true);

        // 查询鲜沐仓库warehouseTenantId=1，sku的配送日期
        List<String> skucodeList = itemId2StockDTOMap.values().stream().filter(e -> XianmuSupplyTenant.TENANT_ID.equals(e.getWarehouseTenantId())).map(StockDTO::getSupplySku).distinct().collect(Collectors.toList());

        DeliveryDateSkuQueryDTO deliveryDateSkuQueryDTO = new DeliveryDateSkuQueryDTO();
        deliveryDateSkuQueryDTO.setTenantId(stockQueryDTO.getTenantId());
        deliveryDateSkuQueryDTO.setStoreId(stockQueryDTO.getMerchantAddressDTO().getStoreId());
        deliveryDateSkuQueryDTO.setCity(stockQueryDTO.getMerchantAddressDTO().getCity());
        deliveryDateSkuQueryDTO.setArea(stockQueryDTO.getMerchantAddressDTO().getArea());
        deliveryDateSkuQueryDTO.setSkuCodeList(skucodeList);
        List<DeliveryDate4SkuDTO> deliveryDate4SkuDTOS = ofcDeliveryInfoFacade.queryDeliveryDateBySku(deliveryDateSkuQueryDTO);
        Map<String, LocalDate> sku2DeliveryDateMap = deliveryDate4SkuDTOS.stream().collect(Collectors.toMap(DeliveryDate4SkuDTO::getSku, DeliveryDate4SkuDTO::getDeliveryDate, (k1, k2) -> k1));

        itemId2StockDTOMap.entrySet().forEach(entry -> {
            StockDTO stockDTO = entry.getValue();
            if(StringUtils.isNotBlank(stockDTO.getSupplySku())){
                stockDTO.setDeliveryDate(sku2DeliveryDateMap.get(stockDTO.getSupplySku()));
                if(!CollectionUtils.isEmpty(deliveryDate4SkuDTOS)) {
                    stockDTO.setFulfillmentType(deliveryDate4SkuDTOS.get(0).getFulfillmentType());
                }
            }
        });

        return itemId2StockDTOMap;
    }

    @Override
    public Map<Long, StockDTO> preDistributionOrderOccupy(StockQueryDTO stockQueryDTO, boolean isOpenApi) {
        Map<Long, StockDTO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(stockQueryDTO.getPreDistributionOrderItemDTOList())) {
            return result;
        }

        List<PreDistributionOrderItemDTO> preDistributionOrderItemDTOList = stockQueryDTO.getPreDistributionOrderItemDTOList();
        // 虚拟货品
        List<Long> virtualItemIds = preDistributionOrderItemDTOList.stream().filter(item -> GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(item.getGoodsType())).map(PreDistributionOrderItemDTO::getItemId).collect(Collectors.toList());

        // 所有商品
        List<Long> allItemIds = preDistributionOrderItemDTOList.stream().map(PreDistributionOrderItemDTO::getItemId).collect(Collectors.toList());
        List<MarketItemVO> marketItemVOS = marketItemService.batchByItemIds(allItemIds, stockQueryDTO.getTenantId());
        Map<Long, MarketItemVO> marketItemMap = marketItemVOS.stream().collect(Collectors.toMap(MarketItemVO::getItemId, item -> item));

        // 查询虚拟货品库存
        if (!CollectionUtils.isEmpty(virtualItemIds)) {
            List<StockResp> stockResps = stockFacade.batchQuery(stockQueryDTO.getTenantId(), virtualItemIds);
            if (!CollectionUtils.isEmpty(stockResps)) {
                stockResps.forEach(stock -> {
                    StockDTO stockDTO = new StockDTO();
                    BeanUtils.copyProperties(stock, stockDTO);
                    MarketItemVO marketItemVO = marketItemMap.get(stock.getItemId());
                    Long supplierTenantId = StringUtils.isBlank(marketItemVO.getSupplierId()) ? stockDTO.getTenantId() : Long.valueOf(marketItemVO.getSupplierId());
                    stockDTO.setSupplyTenantId(supplierTenantId);
                    // 有充足库存
                    stockDTO.setOrderNonOccupy(Boolean.FALSE);
                    stockDTO.setMaxAmount(stock.getAmount());
                    result.put(stock.getItemId(), stockDTO);
                });
            }
        }

        // 按照sku分组
        Map<String, List<PreDistributionOrderItemDTO>> preDistributionOrderItemDTOListMap = preDistributionOrderItemDTOList.stream().filter(item -> !GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(item.getGoodsType()))
                .collect(Collectors.groupingBy(PreDistributionOrderItemDTO::getAgentSku));

        if (CollectionUtils.isEmpty(preDistributionOrderItemDTOListMap)) {
            return result;
        }

        List<PreDistributionOrderItemDTO> distributionOrderItemDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(preDistributionOrderItemDTOListMap)) {
            distributionOrderItemDTOList = preDistributionOrderItemDTOListMap.values().stream().map(preDistributionOrderItemDTOS -> {
                PreDistributionOrderItemDTO preDistributionOrderItemDTO = new PreDistributionOrderItemDTO();
                PreDistributionOrderItemDTO distributionOrderItemDTO = preDistributionOrderItemDTOS.get(NumberConstant.ZERO);
                preDistributionOrderItemDTO.setItemId(distributionOrderItemDTO.getItemId());
                preDistributionOrderItemDTO.setGoodsType(distributionOrderItemDTO.getGoodsType());
                preDistributionOrderItemDTO.setSkuId(distributionOrderItemDTO.getSkuId());
                preDistributionOrderItemDTO.setAgentSku(distributionOrderItemDTO.getAgentSku());
                preDistributionOrderItemDTO.setAgentSkuId(distributionOrderItemDTO.getAgentSkuId());
                int quantity = preDistributionOrderItemDTOS.stream().mapToInt(PreDistributionOrderItemDTO::getQuantity).sum();
                preDistributionOrderItemDTO.setQuantity(quantity);

                boolean flag = preDistributionOrderItemDTOS.stream().allMatch(item -> PresaleSwitchEnum.PRESALE_ITEM.getCode().equals(marketItemMap.getOrDefault(item.getItemId(), new MarketItemVO()).getPresaleSwitch()));
                preDistributionOrderItemDTO.setPresaleSwitch(flag ? PresaleSwitchEnum.PRESALE_ITEM.getCode() : PresaleSwitchEnum.NORMAL_ITEM.getCode());
                preDistributionOrderItemDTO.setSkuTenantId(GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(distributionOrderItemDTO.getGoodsType()) ? XianmuSupplyTenant.TENANT_ID : stockQueryDTO.getTenantId());

                return preDistributionOrderItemDTO;
            }).collect(Collectors.toList());
        }

        // 供应链货品库存查询
        List<PreDistributionSkuDetailReqDTO> preDistributionSkuDetailReqDTOS = distributionOrderItemDTOList.stream().map(item ->
                        SaleInventoryConvert.convertToPreDistributionSkuDetailReqDTO(item))
                .collect(Collectors.toList());


        PreDistributionOrderOccupyReqDTO preDistributionOrderOccupyReqDTO = SaleInventoryConvert.convertToPreDistributionOrderOccupyReqDTO(stockQueryDTO);
        preDistributionOrderOccupyReqDTO.setPreDistributionSkuDetailReqDTOS(preDistributionSkuDetailReqDTOS);
        // 开放平台下单，需要传storeId，查看是否匹配虚拟城配仓
        if(isOpenApi){
            preDistributionOrderOccupyReqDTO.setContactId(stockQueryDTO.getMerchantAddressDTO().getStoreId());
        }

        Set<Long> skuIds = preDistributionOrderItemDTOList.stream().filter(item -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(item.getGoodsType())).map(PreDistributionOrderItemDTO::getSkuId).collect(Collectors.toSet());
        Map<Long, ProductSkuCityPreferentialCostPriceResp> costPriceRespMap = productSkuPreferentialCostPriceFacade.queryPreferentialCostPriceBySkuIds4City (stockQueryDTO.getTenantId (), stockQueryDTO.getMerchantAddressDTO ().getCityId (), skuIds);
        try {
            PreDistributionOrderOccupyResDTO preDistributionOrderOccupyResDTO = saleInventoryCommandFacade.preDistributionOrderOccupy(preDistributionOrderOccupyReqDTO);
            // 可占用库存明细
            List<PreDistributionSkuDetailResDTO> orderOccupyAbleDetailList = preDistributionOrderOccupyResDTO.getOrderOccupableDetailList();
            enableOccupyStockDataConvert(orderOccupyAbleDetailList, result, preDistributionOrderItemDTOListMap,costPriceRespMap);

            // 不可占用库存明细
            List<PreDistributionNotOccupySkuDetailResDTO> orderNonOccupyAbleDetailList = preDistributionOrderOccupyResDTO.getOrderNonOccupableDetailList();
            unableOccupyStockDataConvert(orderNonOccupyAbleDetailList, result, preDistributionOrderItemDTOListMap);
        } catch (Exception e) {
            log.error("供应链库存查询失败，失败原因: {}", e);
        }

        return result;
    }

    /**
     * 可占用库存数据转化
     *
     * @param orderOccupyAbleDetailList          可占用明细
     * @param result                             返回结果
     * @param preDistributionOrderItemDTOListMap 根据sku编码聚合商品项
     */
    private void enableOccupyStockDataConvert(List<PreDistributionSkuDetailResDTO> orderOccupyAbleDetailList, Map<Long, StockDTO> result, Map<String, List<PreDistributionOrderItemDTO>> preDistributionOrderItemDTOListMap,Map<Long, ProductSkuCityPreferentialCostPriceResp> costPriceRespMap) {
        if (CollectionUtils.isEmpty(orderOccupyAbleDetailList)) {
            return;
        }

        orderOccupyAbleDetailList.forEach(preDistributionSkuDetailResDTO -> {
            // 获取单仓最大可占用库存
            List<TryMostOccupableDetail> tryMostOccupableDetailList = preDistributionSkuDetailResDTO.getTryMostOccupableDetailList();
            List<TryMostOccupableDetail> tryMostOccupableDetails = tryMostOccupableDetailList.stream().filter(item -> Objects.nonNull(item.getSaleAbleQuantity())).sorted(Comparator.comparing(TryMostOccupableDetail::getSaleAbleQuantity)).collect(Collectors.toList());

            List<PreDistributionOrderItemDTO> orderItemDTOList = preDistributionOrderItemDTOListMap.get(preDistributionSkuDetailResDTO.getSkuCode());
            if (!CollectionUtils.isEmpty(orderItemDTOList)) {
                for (PreDistributionOrderItemDTO preDistributionOrderItemDTO : orderItemDTOList) {
                    StockDTO stockDTO = new StockDTO();
                    stockDTO.setItemId(preDistributionOrderItemDTO.getItemId());
                    ProductSkuCityPreferentialCostPriceResp preferentialCostPrice = costPriceRespMap.get (preDistributionOrderItemDTO.getSkuId ());
                    if(ObjectUtil.isEmpty (preferentialCostPrice) || ObjectUtil.isEmpty (preferentialCostPrice.getAvailableQuantity ())){
                        // 最大可占用数量
                        stockDTO.setAmount (preDistributionSkuDetailResDTO.getMaxOccupyQuantity ());
                        TryMostOccupableDetail tryMostOccupableDetail = tryMostOccupableDetails.get (tryMostOccupableDetails.size () - 1);
                        stockDTO.setMaxAmount (tryMostOccupableDetail.getSaleAbleQuantity ());
                        stockDTO.setProductSkuPreferentialCostPriceFlag (true);
                    }else {
                        // 最大可占用数量
                        TryMostOccupableDetail tryMostOccupableDetail = tryMostOccupableDetails.get (tryMostOccupableDetails.size () - 1);
                        stockDTO.setAmount (preDistributionSkuDetailResDTO.getMaxOccupyQuantity ()<preferentialCostPrice.getAvailableQuantity ()?preDistributionSkuDetailResDTO.getMaxOccupyQuantity ():preferentialCostPrice.getAvailableQuantity ());
                        stockDTO.setMaxAmount (tryMostOccupableDetail.getSaleAbleQuantity ()<preferentialCostPrice.getAvailableQuantity ()?tryMostOccupableDetail.getSaleAbleQuantity ():preferentialCostPrice.getAvailableQuantity ());
                    }
                    // 仓库编号
                    stockDTO.setWarehouseNo(preDistributionSkuDetailResDTO.getWarehouseNo());
                    // 租户编号 鲜沐为0
                    stockDTO.setWarehouseTenantId(preDistributionSkuDetailResDTO.getWarehouseTenantId());
                    stockDTO.setSkuId(preDistributionOrderItemDTO.getSkuId());
                    stockDTO.setSupplySkuId(preDistributionOrderItemDTO.getAgentSkuId());
                    stockDTO.setSupplySku(preDistributionOrderItemDTO.getAgentSku());
                    stockDTO.setSupplyTenantId(preDistributionOrderItemDTO.getAgentTenantId());
                    // 商品有效期
                    if (Objects.nonNull(preDistributionSkuDetailResDTO.getFruitFlag())) {
                        stockDTO.setQuantityDate(preDistributionSkuDetailResDTO.getFruitFlag() ? null : preDistributionSkuDetailResDTO.getQualityDate());
                    }

                    // 有充足库存
                    stockDTO.setOrderNonOccupy(Boolean.FALSE);
                    result.put(preDistributionOrderItemDTO.getItemId(), stockDTO);
                }
            }
        });

    }

    /**
     * 不可占用数据转化
     *
     * @param orderNonOccupyAbleDetailList
     * @param result
     * @param preDistributionOrderItemDTOListMap
     */
    private void unableOccupyStockDataConvert(List<PreDistributionNotOccupySkuDetailResDTO> orderNonOccupyAbleDetailList, Map<Long, StockDTO> result, Map<String, List<PreDistributionOrderItemDTO>> preDistributionOrderItemDTOListMap) {
        if (CollectionUtils.isEmpty(orderNonOccupyAbleDetailList)) {
            return;
        }

        orderNonOccupyAbleDetailList.forEach(occupySkuDetailResDTO -> {
            List<PreDistributionSkuDetailResDTO> orderOccupableDetailList = occupySkuDetailResDTO.getTryMostOccupableDetailList();
            List<PreDistributionOrderItemDTO> orderItemDTOList = preDistributionOrderItemDTOListMap.get(occupySkuDetailResDTO.getSkuCode());
            if (!CollectionUtils.isEmpty(orderItemDTOList)) {
                for (PreDistributionOrderItemDTO preDistributionOrderItemDTO : orderItemDTOList) {
                    StockDTO stockDTO = new StockDTO();
                    stockDTO.setItemId(preDistributionOrderItemDTO.getItemId());
                    // 最大可占用数量
                    if (!CollectionUtils.isEmpty(orderOccupableDetailList)) {
                        List<PreDistributionSkuDetailResDTO> preDistributionSkuDetailResDTOS = orderOccupableDetailList.stream().filter(item -> Objects.nonNull(item.getMaxOccupyQuantity())).sorted(Comparator.comparing(PreDistributionSkuDetailResDTO::getMaxOccupyQuantity)).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(preDistributionSkuDetailResDTOS)) {
                            PreDistributionSkuDetailResDTO preDistributionSkuDetailResDTO = preDistributionSkuDetailResDTOS.get(preDistributionSkuDetailResDTOS.size() - 1);
                            // 仓库编号
                            stockDTO.setWarehouseNo(preDistributionSkuDetailResDTO.getWarehouseNo());
                            // 租户编号 鲜沐为0
                            stockDTO.setWarehouseTenantId(preDistributionSkuDetailResDTO.getWarehouseTenantId());
                            stockDTO.setAmount(preDistributionSkuDetailResDTO.getMaxOccupyQuantity());
                            stockDTO.setMaxAmount(preDistributionSkuDetailResDTO.getMaxOccupyQuantity());
                        }
                    } else {
                        stockDTO.setAmount(NumberConstant.ZERO);
                    }

                    stockDTO.setSkuId(preDistributionOrderItemDTO.getSkuId());
                    stockDTO.setSupplySkuId(preDistributionOrderItemDTO.getAgentSkuId());
                    stockDTO.setSupplySku(preDistributionOrderItemDTO.getAgentSku());
                    stockDTO.setSupplyTenantId(preDistributionOrderItemDTO.getAgentTenantId());
                    // 商品有效期
                    if (Objects.nonNull(occupySkuDetailResDTO.getFruitFlag())) {
                        stockDTO.setQuantityDate(occupySkuDetailResDTO.getFruitFlag() ? null : occupySkuDetailResDTO.getQualityDate());
                    }

                    // 没有充足库存
                    stockDTO.setOrderNonOccupy(Boolean.TRUE);
                    result.put(preDistributionOrderItemDTO.getItemId(), stockDTO);
                }
            }
        });
    }

    /**
     * 自营仓订单冻结库存
     *
     * @param orderDto
     */
    @Override
    public void orderOccupyBySpecifyWarehouseAndSku(OrderDTO orderDto) {
        OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO = SaleInventoryConvert.convertToOrderOccupyBySpecifyWarehouseReqDTO(orderDto);
        orderOccupyBySpecifyWarehouseReqDTO.setOperatorName(userCenterMerchantStoreFacade.queryStoreName(orderDto.getStoreId()));
        try {
            saleInventoryCommandFacade.orderOccupyBySpecifyWarehouseAndSku(orderOccupyBySpecifyWarehouseReqDTO);
            // 更新订单状态为待支付
            OrderStatusUpdateReq orderStatusUpdateReq = new OrderStatusUpdateReq();
            orderStatusUpdateReq.setOrderId(orderDto.getId());
            orderStatusUpdateReq.setOriginStatus(com.cosfo.ordercenter.client.common.OrderStatusEnum.CREATING_ORDER.getCode());
            orderStatusUpdateReq.setStatus(com.cosfo.ordercenter.client.common.OrderStatusEnum.NO_PAYMENT.getCode());
            RpcResultUtil.handle(orderCommandProvider.updateStatus(orderStatusUpdateReq));
//            orderMapper.updateOrderStatusById(orderDto.getId(), OrderStatusEnum.CREATING_ORDER.getCode(), OrderStatusEnum.NO_PAYMENT.getCode(), null);
        } catch (Exception e) {
            // 订单冻结库存失败
            log.error("自营仓订单{}冻结库存失败", orderDto.getOrderNo());
            // 更新订单状态为取消
//            orderMapper.updateOrderStatusById(orderDto.getId(), OrderStatusEnum.CREATING_ORDER.getCode(), OrderStatusEnum.CANCELED.getCode(), null);
            OrderStatusUpdateReq orderStatusUpdateReq = new OrderStatusUpdateReq();
            orderStatusUpdateReq.setOrderId(orderDto.getId());
            orderStatusUpdateReq.setOriginStatus(com.cosfo.ordercenter.client.common.OrderStatusEnum.CREATING_ORDER.getCode());
            orderStatusUpdateReq.setStatus(com.cosfo.ordercenter.client.common.OrderStatusEnum.CANCELED.getCode());
            RpcResultUtil.handle(orderCommandProvider.updateStatus(orderStatusUpdateReq));
        }
    }
}
