package com.cosfo.mall.stock.model.dto;

import com.cosfo.mall.stock.model.po.Stock;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2022/5/17  14:53
 */
@Data
public class StockDTO extends Stock {
    /**
     * 供应商skuId
     */
    private Long supplySkuId;
    /**
     * 供应商租户Id
     */
    private Long supplyTenantId;

    /**
     * 生产日期
     */
    private LocalDate quantityDate;

    private LocalDate productionDate;
    /**
     * 货品skuId
     */
    private Long skuId;
    /**
     * 供应商sku编码
     */
    private String supplySku;
    /**
     * 仓库租户id(saas品牌方)，鲜沐为1
     */
    private Long warehouseTenantId;
    /**
     * 仓库编码, 为空标志无法调度到仓库
     */
    private Long warehouseNo;

    /**
     * 订单无法占用库存
     */
    private Boolean orderNonOccupy;

    /**
     * 是否占用省心定库存
     */
    private boolean productSkuPreferentialCostPriceFlag;

    /**
     * sku配送日期，只有鲜沐仓库warehouseTenantId=1时，才有值，鲜沐自营品、代销不入仓品配送日期可能不同
     */
    private LocalDate deliveryDate;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;
}
