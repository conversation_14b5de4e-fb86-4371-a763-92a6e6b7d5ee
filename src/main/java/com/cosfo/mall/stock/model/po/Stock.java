package com.cosfo.mall.stock.model.po;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * stock
 * <AUTHOR>
@Data
public class Stock implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * itemId
     */
    private Long itemId;

    /**
     * 库存 - 前端显示
     */
    private Integer amount;

    /**
     * 最大可占用库存
     */
    private Integer maxAmount;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}