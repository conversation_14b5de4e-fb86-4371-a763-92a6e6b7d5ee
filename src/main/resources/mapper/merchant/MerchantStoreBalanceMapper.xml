<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.merchant.mapper.MerchantStoreBalanceMapper">

    <update id="decreaseBalance">
        update merchant_store_balance
        set balance = balance - #{changeBalance}
        where id = #{id} and balance >= #{changeBalance}
    </update>
    <update id="increaseBalance">
        update merchant_store_balance
        set balance = balance + #{changeBalance}
        where id = #{id}
    </update>

    <resultMap id="BaseResultMap" type="com.cosfo.mall.merchant.model.po.MerchantStoreBalance">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="store_id" jdbcType="INTEGER" property="storeId"/>
        <result column="store_no" jdbcType="VARCHAR" property="storeNo"/>
        <result column="balance" jdbcType="DECIMAL" property="balance"/>
        <result column="frozen_balance" jdbcType="DECIMAL" property="frozenBalance"/>
        <result column="account_type" jdbcType="INTEGER" property="accountType"/>
        <result column="fund_account_id" jdbcType="BIGINT" property="fundAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, tenant_id, store_id, store_no, balance,create_time, update_time, frozen_balance, account_type, fund_account_id
    </sql>

    <update id="freezeBalance">
        update merchant_store_balance
        set frozen_balance = frozen_balance + #{changeBalance}, balance = balance - #{changeBalance}
        where id = #{id} and balance >= #{changeBalance}
    </update>

    <update id="decreaseFreezeBalance">
        update merchant_store_balance
        set frozen_balance = frozen_balance - #{changeBalance}
        where id = #{id} and frozen_balance >= #{changeBalance}
    </update>

    <select id="queryNonCashAccountByStoreIdForUpdate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from merchant_store_balance
        where tenant_id = #{tenantId} and store_id = #{storeId} and account_type = 1
        for update
    </select>
</mapper>
